syntax = "proto3";

package codec.in.scheduling;

message Scheduling {
  int32 idx = 1;                     // Índice do agendamento
  int32 start_time = 2;              // <PERSON><PERSON><PERSON><PERSON> de início (em minutos desde a meia-noite)
  int32 days_of_week = 3;            // Dias da semana em que o agendamento é válido (bitmask: 0b01111111)
  int32 number_of_steps = 4;         // Número total de etapas a executar no agendamento
  int32 waterpump_idx = 5;           // Índice da bomba de água usada
  int32 waterpump_working_time = 6;  // Tempo de funcionamento da bomba de água (em minutos)
  bool  allow_ferti = 7;             // Permite aplicação de fertilizante (1 = sim, 0 = não)
  int32 ferti_idx = 8;               // Índice da ferti
  bool  allow_backwash = 9;          // Permite retrolavagem (1 = sim, 0 = não)
  int32 backwash_idx = 10;           // Índice da retrolavagem
  int32 group = 11;                  // Grupo ao qual o agendamento pertence
  bool  once = 12;                   // Não repetir o agendamento
}

message DeviceScheduling {
  int32 idx = 1;                  // Índice do agendamento do dispositivo
  int32 scheduling_idx = 2;       // Índice do agendamento principal
  int32 device_idx = 3;           // Índice do dispositivo
  int32 order = 4;                // Ordem de execução no agendamento
  int32 sector_working_time = 5;  // Tempo de início relativo (minutos após início do agendamento)
  int32 ferti_working_time = 6;   // Tempo de funcionamento para fertilização (em minutos)
  int32 ferti_delay = 7;          // Atraso antes da fertilização (em minutos)
}

message SchedulingPackage {
  MsgType type = 1;
  repeated Scheduling scheduling_data = 2;      // Lista de agendamentos
  repeated DeviceScheduling device_scheduling_data = 3;   // Lista de agendamentos por dispositivo
}

enum MsgType {
  MSG_NONE    = 0;
  MSG_SCHEDULING_ONLY = 1;
  MSG_DEV_SCHEDULING_ONLY = 2;
  MSG_SCHEDULING_ALL = 3;
}
