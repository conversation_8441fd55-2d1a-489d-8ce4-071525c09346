syntax = "proto3";

package codec.out.scheduling_report;

message SchedulingReportData {
  int32  scheduling_idx = 1;       // Índice do agendamento
  uint64 start_time = 2;           // Timestamp do início do agendamento
  uint64 end_time = 3;             // Timestamp do fim do agendamento
  uint64 sector_bitmask1 = 4;      // Bitmask de setores acionados, mascara com 64bits iniciais
  uint64 sector_bitmask2 = 5;      // Bitmask de setores acionados, mascara com mais 64bits
  uint64 ferti_bitmask1 = 6;       // Bitmask da ferti de setores acionados, mascara com 64bits iniciais
  uint64 ferti_bitmask2 = 7;       // Bitmask da ferti de setores acionados, mascara com mais 64bits
  bool   waterpump = 8;            // Estado da bomba de água (1 = ligou, 0 = não ligou)
  uint64 backwash_time = 9;        // Timestamp de início da retrolavagem, se teve retrolavagem
  int32  number_of_sectors = 10;   // Número de setores do agendamento
  bool   had_waterpump = 11;       // Se usou a bomba de agua
  bool   had_ferti = 12;           // Se teve aplicação de fertilizante (1 = sim, 0 = não)
  uint64 time_of_resumption = 13;  // Hora do início da retomada
  int32  resumption_attempts = 14; // Número de tentativas da retomada
  int32  status = 15;              // Código de status do agendamento
}

message SchedulingReportPackage {
  repeated SchedulingReportData data = 1;     // Lista de relatórios
}
