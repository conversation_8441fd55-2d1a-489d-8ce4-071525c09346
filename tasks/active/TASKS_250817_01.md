# Task list info:

- name: 250817_01
- base_branch: develop

---

# Tasks

## Task 1. Add safety time to reservoir

**Description**
When a reservoir monitor signal that a reservoir level is low, the LIC will turn on the service pump associated with the reservoir to refill the reservoir.
When the reservoir is full, the reservoir monitor will signal that the reservoir is full and the LIC will turn off the service pump.
However, we need a water pump safety operation time to avoid the case the reservoir monitor fails to signal that the reservoir is full and the pump keeps running.
This safety time must be configurable in the UI and stored in the database and it is a property of the reservoir.
Its unit is minutes.

**Target directories**

- app (frontend)
- directus (backend)
- docs (documentation)

**Status:** Done

### Subtask 1.1. Add safety_time field to reservoir database schema

**Description**
Create database migration to add a safety_time field to the reservoir table with appropriate data type (integer for minutes), constraints, and indexing

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.2. Configure Directus for reservoir safety time field

**Description**
Update Directus collection and field configurations to expose the safety_time field in the admin panel and API, including proper permissions and validation rules

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.3. Add safety time configuration to reservoir form

**Description**
Update the ReservoirForm component to include the safety_time field with proper input validation, labels, and integration with the existing form structure

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 1.4. Update documentation for reservoir safety time feature

**Description**
Update relevant documentation files to reflect the new safety time field, including entity documentation, DDL changes, and business rules

**Target directories**

- docs (documentation)

**Status:** Done

## Task 2. Implement SchedulingPackage protobuf message builder

**Description**
Implement a function that, given a LIC identifier, creates a codec.in\_.scheduling.SchedulingPackage message with the current scheduling configuration for that LIC.
It is a complex task that involves investigating the Android app code in /irriganet and relate it to the irriga mais data structure in order to understand what data is needed to build the message.
Use /mqtt-integration/src/proto/builder/devices.ts for this implementation: Make use intermediary types and functions, like VirtualDevice and generateVirtualDevices, to build the message.

In the kotlin code of the Android app, important files are:

- /irriganet/app/src/main/java/br/com/byagro/irriganet/MainActivity.kt - SchedulingPackage message construction beginning at line 372
- /irriganet/app/src/main/java/br/com/byagro/irriganet/DBHelper.kt - Data access methods used in MainActivity.kt
- /irriganet/app/src/main/java/br/com/byagro/irriganet/ui/SectorSchedulingFragment.kt - UI code for sector scheduling configuration

In the irriga mais side, relevant information is:

- Project entity act as the group of the android app (see /mqtt-integration/src/proto/builder/devices.ts)
- IrrigationPlan, IrrigationPlanStep, and Sector entities are relevant to build the SchedulingPackage message
- /docs/DDL.md has the database schema

It is crucial that you understand the meaning of the SchedulingPackage protobuf message and how it is built in the Android app before starting this task. Then you must make use of your understanding of the irriga mais data structure to build the message on this side.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.1. Analyze Android app scheduling logic and create mapping documentation

**Description**
Study the Android app's MainActivity.kt scheduling construction logic, DBHelper methods, and SectorSchedulingFragment to understand the data flow and create comprehensive mapping documentation between Android SQLite schema and Irriga Mais PostgreSQL schema.

**Target directories**

- irriganet (analysis)
- mqtt-integration (documentation)

**Status:** Done

### Subtask 2.2. Implement database queries for irrigation scheduling data

**Description**
Create database query functions to retrieve irrigation plans, plan steps, and related entities (sectors, devices) for a given LIC identifier. Include proper joins and filtering for active records at reference date.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.3. Implement SchedulingPackage message builder core logic

**Description**
Create the main `buildSchedulingPackage` function that uses the database queries to construct `Scheduling` and `DeviceScheduling` messages according to the Android app logic mapping.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.4. Add comprehensive testing and validation

**Description**
Create tests for the scheduling package builder, including edge cases, data validation, and comparison with expected Android app output format. Ensure integration with existing message handling.

**Target directories**

- mqtt-integration (testing)

**Status:** Canceled

## Task 3. Add backwash_enabled to IrrigationPlan

**Description**
Protobuf Scheduling message in /protobuf/proto/scheduling.proto has a field named `allow_backwash`. This must be reflected in the irriga mais database and UI.
Add a backwash_enabled field to the IrrigationPlan entity. The field must be a boolean and must be configurable in the UI and stored in the database. It must be included in the SchedulingPackage message.

**Target directories**

- directus (backend)
- app (frontend)
- mqtt-integration (backend)
- docs (documentation)

**Status:** Done

### Subtask 3.1. Re-add backwash_enabled field to IrrigationPlan database schema

**Description**
Create a new database migration to add the `backwash_enabled` boolean field back to the `irrigation_plan` table with proper defaults and constraints.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 3.2. Update IrrigationPlan TypeScript models and interfaces

**Description**
Update the IrrigationPlan interface in `app/src/api/model/irrigation-plan.ts` and IrrigationPlanFormData interface to include the `backwash_enabled` field.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 3.3. Add backwash toggle to irrigation plan configuration UI

**Description**
Add a backwash toggle section to the IrrigationPlanConfigPanel component, following the same pattern as the fertigation toggle with appropriate project validation.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 3.4. Update scheduling package builder to use plan's backwash_enabled

**Description**
Modify the `buildSchedulingPackage` function to use `plan.backwash_enabled` instead of deriving backwash allowance from project configuration only.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 3.5. Update documentation for backwash_enabled field

**Description**
Update entity documentation and DDL documentation to reflect the new backwash_enabled field in IrrigationPlan.

**Target directories**

- docs (documentation)

**Status:** Done
