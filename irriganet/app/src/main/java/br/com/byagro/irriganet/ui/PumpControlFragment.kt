package br.com.byagro.irriganet.ui

import HiveMqttManager
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.MainActivity
//import br.com.byagro.irriganet.MqttManager
import br.com.byagro.irriganet.SharedData
import br.com.byagro.irriganet.Utils
import br.com.byagro.irriganet.WebService
import br.com.byagro.irriganet.databinding.FragmentPumpControlBinding
import codec.`in`.IncomingPacketOuterClass
import codec.`in`.control.Control
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [PumpControlFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class PumpControlFragment : Fragment() {
    private val PREFS_NAME = "byagro_prefs"
    private lateinit var binding: FragmentPumpControlBinding
    private lateinit var dbHelper: DBHelper
    private lateinit var sharedPref: SharedPreferences

    private var deviceIdx: Int = -1
    private var mqttManager: HiveMqttManager? = null
    private var levelPump: Boolean? = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            deviceIdx = it.getInt("deviceIdx", -1)
        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentPumpControlBinding.inflate(inflater, container, false)

        try {
            val activity = activity as? MainActivity
            mqttManager = activity?.getMqttManager()

            sharedPref = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

            levelPump = dbHelper.getLevelPumpEnableByDevice(deviceIdx)
            if (levelPump != null) {
                binding.fragPumpControlSwitchAuto.visibility = View.VISIBLE
                binding.fragPumpControlSwitchAuto.isChecked = levelPump ?: false
            } else {
                binding.fragPumpControlSwitchAuto.visibility = View.GONE
            }

            binding.fragPumpControlButtonTurnOn.setOnClickListener {
                val workingTime = binding.fragPumpControlTextWorkingTime.text.toString()
                if (workingTime.isEmpty()) {
                    Toast.makeText(
                        requireContext(),
                        "Preencha a duração!",
                        Toast.LENGTH_SHORT
                    ).show()
                    return@setOnClickListener
                }

                if (deviceIdx != -1) {
                    val device = dbHelper.getDeviceByIdx(deviceIdx)
                    val codecIdentity = device?.get("codec_identity") as String ?: ""
                    val type = device?.get("type") as Int ?: 0

                    val map = device?.let {
                        val idx = (it["ord_idx"] as? Number)?.toInt()
                        if (idx != null) {
                            val control = Control.ControlPackage.newBuilder()
                                .setIdx(idx)
                                .setAction(Control.MsgAction.MSG_TURN_ON)
                                .setValue(workingTime.toInt())
                                .build()

                            val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder()
                                .setId(System.currentTimeMillis() / 1000)
                                .setControl(control)
                                .build()

                            val payload: ByteArray = packet.toByteArray()
                            val crc2: Int = Utils.crc16(payload)
                            val crcBytes = byteArrayOf(((crc2 ushr 8) and 0xFF).toByte(), (crc2 and 0xFF).toByte())
                            val finalPayload = payload + crcBytes

                            val topic = "/codec/$codecIdentity/downlink"
                            if (SharedData.codecWifiIsConnected.get() && SharedData.codecId.get() == codecIdentity) {
                                WebService.Request(codecIdentity, finalPayload, requireActivity(), ::handleResponse)
                            } else if (mqttManager?.isConnected() == true) {
                                lifecycleScope.launch(Dispatchers.IO) {
                                    if(mqttManager?.publish(topic, finalPayload) == true) {
                                        requireActivity().runOnUiThread {
                                            Toast.makeText(requireContext(), "Comando enviado com sucesso.", Toast.LENGTH_SHORT).show()
                                        }
                                    } else {
                                        requireActivity().runOnUiThread {
                                            Toast.makeText(requireContext(), "Erro ao enviar o comando!", Toast.LENGTH_LONG).show()
                                        }
                                    }
                                }
                            } else {
                                requireActivity().runOnUiThread {
                                    Toast.makeText(requireContext(), "Falha no conexão com o servidor!", Toast.LENGTH_LONG).show()
                                }
                            }
                        } else {
                            null
                        }
                    }
                }
            }

            binding.fragPumpControlButtonTurnOff.setOnClickListener {
                if (deviceIdx != -1) {
                    val device = dbHelper.getDeviceByIdx(deviceIdx)
                    val codecIdentity = device?.get("codec_identity") as String ?: ""
                    val type = device?.get("type") as Int ?: 0

                    val map = device?.let {
                        val idx = (it["ord_idx"] as? Number)?.toInt()
                        if (idx != null) {
                            val control = Control.ControlPackage.newBuilder()
                                .setIdx(idx)
                                .setAction(Control.MsgAction.MSG_TURN_OFF)
                                .setValue(0)
                                .build()

                            val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder()
                                .setId(System.currentTimeMillis() / 1000)
                                .setControl(control)
                                .build()

                            val payload: ByteArray = packet.toByteArray()
                            val crc2: Int = Utils.crc16(payload)
                            val crcBytes = byteArrayOf(((crc2 ushr 8) and 0xFF).toByte(), (crc2 and 0xFF).toByte())
                            val finalPayload = payload + crcBytes

                            val topic = "/codec/$codecIdentity/downlink"
                            if (SharedData.codecWifiIsConnected.get() && SharedData.codecId.get() == codecIdentity) {
                                WebService.Request(codecIdentity, finalPayload, requireActivity(), ::handleResponse)
                            } else if (mqttManager?.isConnected() == true) {
                                lifecycleScope.launch(Dispatchers.IO) {
                                    if(mqttManager?.publish(topic, finalPayload) == true) {
                                        requireActivity().runOnUiThread {
                                            Toast.makeText(requireContext(), "Comando enviado com sucesso.", Toast.LENGTH_SHORT).show()
                                        }
                                    } else {
                                        requireActivity().runOnUiThread {
                                            Toast.makeText(requireContext(), "Erro ao enviar o comando!", Toast.LENGTH_LONG).show()
                                        }
                                    }
                                }
                            } else {
                                requireActivity().runOnUiThread {
                                    Toast.makeText(requireContext(), "Falha no conexão com o servidor!", Toast.LENGTH_LONG).show()
                                }
                            }
                        } else {
                            null
                        }
                    }
                }
            }
        } catch (e: Exception){
            e.printStackTrace()
        }

        return binding.root
    }

    override fun onPause() {
        super.onPause()
        if(levelPump != null){
            if(levelPump != binding.fragPumpControlSwitchAuto.isChecked) {
                dbHelper.updateLevelPumpEnableByDevice(deviceIdx, binding.fragPumpControlSwitchAuto.isChecked)
                val timeStamp = System.currentTimeMillis() / 1000
                val codecIdx = dbHelper.getCodecIdxByDevice(deviceIdx)
                dbHelper.updateCodecFields(
                    codecIdx ?: 0, mapOf(
                        "last_automation_update" to timeStamp,
                    )
                )
                with(sharedPref.edit()) {
                    putBoolean("dataUpdated", true)
                    putBoolean("dataUpdateEvent", true)
                    apply()
                }
            }
        }
    }

    fun handleResponse(identity: String?, response: ByteArray?) {

    }
}