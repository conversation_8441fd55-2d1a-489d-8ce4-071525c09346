package br.com.byagro.irriganet.ui

import android.app.AlertDialog
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.MeshDeviceAdapter
import br.com.byagro.irriganet.MeshDeviceItem
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.databinding.FragmentDeviceRegistrationBinding

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [DeviceRegistrationFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class DeviceRegistrationFragment : Fragment() {
    private val PREFS_NAME = "byagro_prefs"
    private lateinit var binding: FragmentDeviceRegistrationBinding
    private lateinit var dbHelper: DBHelper
    private lateinit var sharedPref: SharedPreferences
    private var codecIdx: Int = -1
    private var groupIdx: Int = -1
    private var groupName: String? = null

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: MeshDeviceAdapter
    private var meshDeviceList: MutableList<MeshDeviceItem> = mutableListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            codecIdx = it.getInt("codecIdx")
            groupIdx = it.getInt("groupIdx")
            groupName = it.getString("groupName")
        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        //val view = inflater.inflate(R.layout.fragment_group, container, false)
        binding = FragmentDeviceRegistrationBinding.inflate(inflater, container, false)

        try {
            //recyclerView = view.findViewById(R.id.recycler_view_mesh_devices)
            sharedPref = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

            binding.fragDeviceRegRecyclerViewMeshDevices.layoutManager =
                LinearLayoutManager(context)

            adapter = MeshDeviceAdapter(
                meshDeviceList,
                { selectedItem -> onItemClick(selectedItem) },
                { position -> deleteMeshDevice(position) })
            binding.fragDeviceRegRecyclerViewMeshDevices.adapter = adapter

            binding.fragDeviceRegFabAddMeshDevice.setOnClickListener {
                val bundle = Bundle().apply {
                    putInt("meshIdx", -1)
                    putString("meshIdentity", null)
                    putString("name", null)
                    putInt("codecIdx", codecIdx)
                    putInt("groupIdx", groupIdx)
                }
                findNavController().navigate(R.id.action_nav_group_to_nav_mesh_device, bundle)
            }

            meshDeviceList.clear()
            meshDeviceList.addAll(dbHelper.getAllMeshDevices(groupIdx))
            adapter.notifyDataSetChanged()

        } catch (e: Exception) {
            e.printStackTrace()
        }
        //return view
        return binding.root
    }

    private fun onItemClick(Item: MeshDeviceItem) {
        val bundle = Bundle().apply {
            putInt("meshIdx", Item.idx)
            putString("meshIdentity", Item.identity)
            putString("name", Item.name)
            putInt("codecIdx", codecIdx)
            putInt("groupIdx", groupIdx)
        }
        findNavController().navigate(R.id.action_nav_group_to_nav_mesh_device, bundle)
    }

    private fun deleteMeshDevice(position: Int) {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle("Confirmar Exclusão")
        builder.setMessage("Tem certeza de que deseja excluir este Dispositivo Mesh?")

        builder.setPositiveButton("Sim") { dialog, _ ->

            val Id = meshDeviceList[position].idx
            val rowsDeleted = dbHelper.deleteMeshDevice(Id)

            if (rowsDeleted > 0) {
                meshDeviceList.removeAt(position) // Remove item from the list
                adapter.notifyItemRemoved(position) // Notify adapter about the change
            }

            val timeStamp = System.currentTimeMillis() / 1000
            val codecIdx = dbHelper.getCodecIdxByGroup(groupIdx)
            dbHelper.updateCodecFields(
                codecIdx ?: 0, mapOf(
                    "last_devices_update" to timeStamp,
                    "last_automation_update" to timeStamp,
                    "last_scheduling_update" to timeStamp,
                    "last_device_scheduling_update" to timeStamp,
                )
            )
            with(sharedPref.edit()) {
                putBoolean("dataUpdated", true)
                putBoolean("dataUpdateEvent", true)
                apply()
            }

            dialog.dismiss()
        }

        builder.setNegativeButton("Cancelar") { dialog, _ ->
            dialog.dismiss() // Close dialog without deleting
        }

        val alertDialog = builder.create()
        alertDialog.show()
    }

}