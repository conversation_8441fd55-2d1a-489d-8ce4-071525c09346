package br.com.byagro.irriganet.ui

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.GroupItem
import br.com.byagro.irriganet.IrrigationGroupAdapter
import br.com.byagro.irriganet.MainActivity
import br.com.byagro.irriganet.MainActivity.LatestCodecUpdates
import br.com.byagro.irriganet.MqttViewModel
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.ReportAdapter
import br.com.byagro.irriganet.ReportItem
import br.com.byagro.irriganet.SharedData
import br.com.byagro.irriganet.WebService
import br.com.byagro.irriganet.databinding.FragmentReportBinding
import codec.out.OutgoingPacketOuterClass
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.google.protobuf.InvalidProtocolBufferException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [ReportFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class ReportFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private val PREFS_NAME = "byagro_prefs"
    private lateinit var binding: FragmentReportBinding
    private lateinit var dbHelper: DBHelper
    private lateinit var sharedPref: SharedPreferences

    private lateinit var adapter: ReportAdapter
    private var reportList: MutableList<ReportItem> = mutableListOf()
    private lateinit var mqttViewModel: MqttViewModel
    private lateinit var codecList: List<Map<String, Any>>
    private lateinit var latestCodecsUpdates:  Map<String, LatestCodecUpdates>
    private var job: Job? = null
    private var countCodecPaused: Int? = null
    private lateinit var schedulingList: List<Map<String, Any>>
    private lateinit var devSchedulingList: List<Map<String, Any>>
    private var messageMap = mutableMapOf<String, ByteArray>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {

        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentReportBinding.inflate(inflater, container, false)

        try {
            binding.fragReportRecyclerReport.layoutManager = LinearLayoutManager(context)

            adapter = ReportAdapter(reportList, { selectedItem -> onItemClick(selectedItem) })
            binding.fragReportRecyclerReport.adapter = adapter

            sharedPref = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            schedulingList = dbHelper.getAllSchedulingsOnMap()
            devSchedulingList = dbHelper.getAllDeviceSchedulingsOnMap()
            codecList = dbHelper.getAllCodecsOnMap()

            val devicesCount = dbHelper.getDevicesCount().toString()

            binding.fragReportTextViewCodecs.text = codecList.size.toString()
            binding.fragReportTextViewGroups.text = dbHelper.getGroupsCount().toString()
            binding.fragReportTextViewSectors.text = dbHelper.getSectorsCount().toString()
            binding.fragReportTextViewSyncedDevicesTotal.text = "/ "+devicesCount.toString()
            binding.fragReportTextViewSyncedDevicesCount.text = "-"

            mqttViewModel = ViewModelProvider(requireActivity()).get(MqttViewModel::class.java)
            val rainGaugeEnabled = sharedPref.getBoolean("rainGaugeEnabled", false)

            val activity = activity as MainActivity

            job = CoroutineScope(Dispatchers.IO).launch {
                while (true) {
                    try {
                        latestCodecsUpdates = activity.getLatestCodecsUpdates()

                        withContext(Dispatchers.Main) {
                            try {

                                if(sharedPref.getBoolean("dataUpdated", false)) {
                                    binding.fragReportImageViewSyncStatus.setColorFilter(Color.parseColor("#F44336"))
                                } else {
                                    binding.fragReportImageViewSyncStatus.setColorFilter(Color.parseColor("#4CAF50"))
                                }

                                var countSyncedDevicesText: String? = null
                                var countSyncedDevices = 0
                                var rainStatus = false
                                var rainFall: Int? = null
                                var rainFallCount: Int = 0
                                var hasStatus = false
                                for (codec in codecList) {
                                    try {
                                        val identity = codec["identity"] as String
                                        val status = latestCodecsUpdates[identity] ?: continue

                                        countSyncedDevices += status.syncBitmask!!.countOneBits()

                                        rainStatus = rainStatus || (status.rainStatus?: false)
                                        rainFall = (rainFall?: 0) + (status.rainfall?: 0)
                                        rainFallCount += 1

                                        hasStatus = true
                                    } catch (_: Exception){
                                    }
                                }
                                if(hasStatus) {
                                    countSyncedDevicesText = countSyncedDevices.toString()
                                    rainFall = rainFall?.div(rainFallCount)
                                    if(countSyncedDevices == devicesCount.toInt()){
                                        binding.fragReportImageViewDevices.setColorFilter(Color.parseColor("#4CAF50"))
                                    }else{
                                        binding.fragReportImageViewDevices.setColorFilter(Color.parseColor("#FFEB3B"))
                                    }
                                } else {
                                    binding.fragReportImageViewDevices.setColorFilter(Color.parseColor("#000000"))
                                }

                                if(rainGaugeEnabled){
                                    binding.fragReportLinearLayoutRainStatusContainer.visibility = View.VISIBLE
                                    if (rainStatus) {
                                        binding.fragReportImageViewRainStatus.setColorFilter(Color.parseColor("#1E90FF"))
                                    } else {
                                        binding.fragReportImageViewRainStatus.setColorFilter(Color.parseColor("#000000"))
                                    }
                                    if(rainFall != null) {
                                        binding.fragReportTextViewRainAmount.visibility = View.VISIBLE
                                        binding.fragReportTextViewRainAmount.text = (rainFall ?: 0).toString() + " mm"
                                    } else {
                                        binding.fragReportTextViewRainAmount.visibility = View.GONE
                                    }
                                } else {
                                    binding.fragReportLinearLayoutRainStatusContainer.visibility = View.GONE
                                }

                                var codecsOnline = false
                                if(SharedData.codecWifiIsConnected.get()){ // Verifica se está conectado no WiFi do Codec
                                    if (latestCodecsUpdates[SharedData.codecId.get().toString()]?.lastInfo!! - System.currentTimeMillis() / 1000 < 60) { // Verifica se o Codec está online
                                        codecsOnline = true
                                    }
                                    if(codecList.size > 1 || countSyncedDevicesText.isNullOrEmpty()) {
                                        countSyncedDevicesText = "-"
                                    }
                                } else {
                                    var countCodecsOnline = 0
                                    for (codec in codecList) {
                                        val identity = codec["identity"] as String
                                        if (latestCodecsUpdates[identity]?.lastInfo!! - System.currentTimeMillis() / 1000 < 60) {
                                            countCodecsOnline += 1 // Conta os codecs online
                                        }
                                    }
                                    if(countCodecsOnline == codecList.size){ // Se todos os codecs estiverem online
                                        codecsOnline = true
                                    }
                                    if(countSyncedDevicesText.isNullOrEmpty()) {
                                        countSyncedDevicesText = "-"
                                    }
                                }
                                binding.fragReportTextViewSyncedDevicesCount.text = countSyncedDevicesText

                                if (codecsOnline) {
                                    binding.fragReportImageViewCodecs.setColorFilter(Color.parseColor("#4CAF50"))
                                } else {
                                    binding.fragReportImageViewCodecs.setColorFilter(Color.parseColor("#F44336"))
                                }

                                if(countCodecPaused != null) {
                                    if ((countCodecPaused ?: 0) > 0) {
                                        binding.fragReportImageViewPausedStatus.visibility = View.VISIBLE
                                    } else {
                                        binding.fragReportImageViewPausedStatus.visibility = View.GONE
                                    }
                                }
                            } catch (e: Exception){
                                //e.printStackTrace()
                            }
                        }

                        if(latestCodecsUpdates.isNotEmpty()) {
                            countCodecPaused = 0
                            for (codec in codecList) {
                                try {
                                    val identity = codec["identity"] as String
                                    if (latestCodecsUpdates[identity]?.pauseScheduling!! == true) { // Verifica se o agendamento está pausado
                                        countCodecPaused = (countCodecPaused ?: 0) + 1
                                    }
                                } catch (e: Exception) {
                                    e.printStackTrace()
                                }
                            }
                        }
                        if(sharedPref.getBoolean("pauseScheduling", false) && countCodecPaused == 0){
                            with(sharedPref.edit()) {
                                putBoolean("pauseScheduling", false)
                                putBoolean("dataUpdateEvent", true)
                                apply()
                            }
                        }

                        withContext(Dispatchers.Main) {
                            updateReportList()
                        }

                    } catch (e: Exception){
                        e.printStackTrace()
                    }

                    delay(1000)
                }
            }

            mqttViewModel.mqttMessage.observe(viewLifecycleOwner) { newMessage ->
                try {
                    Log.d("Report", "MQTT message: codec=${newMessage.codec}, message=${newMessage.message}")
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }

        return binding.root
    }

    fun msbIndex(value: Int): Int? = if (value == 0) null else 31 - Integer.numberOfLeadingZeros(value)

    private fun updateReportList() {
        try {
            reportList.clear()  // Limpa a lista antes de atualizar tudo

            val messages = (requireActivity() as MainActivity).getMessageList()

            for ((key, data) in messages) {
                var (codecIdentity, caseNumber) = key

                if(caseNumber == OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.SCHEDULING_REPORT.number) {
                    val packet = try {
                        OutgoingPacketOuterClass.OutgoingPacket.parseFrom(data)
                    } catch (e: InvalidProtocolBufferException) {
                        Log.e("Protobuf", "Failed to parse packet", e)
                        continue
                    }

                    val schedulingReport = packet.schedulingReport
                    val dataList = schedulingReport.dataList

                    dataList.forEach { entry ->
                        val schedulingIdx = entry.schedulingIdx
                        val scheduling = schedulingList.find {
                            it["ord_idx"] == schedulingIdx && it["codec_identity"] == codecIdentity
                        }

                        if(scheduling.isNullOrEmpty()) return@forEach

                        //val numberOfSteps = scheduling?.get("number_of_steps") as? Int ?: 0
                        //val allowFerti = scheduling?.get("allow_ferti") as? Int ?: 0
                        //val allowBackwash = scheduling?.get("allow_backwash") as? Int ?: 0

                        val s1 = entry.sectorBitmask1.toInt()
                        val f1 = entry.fertiBitmask1.toInt()
                        val cp = entry.status
                        val wp = entry.waterpump
                        val bw = entry.backwashTime.toInt()
                        val st = entry.startTime
                        val ns = entry.numberOfSectors
                        val hw = entry.hadWaterpump
                        val hf = entry.hadFerti
                        val rt = entry.timeOfResumption
                        val ra = entry.resumptionAttempts

                        val nOrder = msbIndex(s1)?:0
                        val devScheduling = devSchedulingList.find {
                            it["scheduling_ord_idx"] == schedulingIdx && it["codec_identity"] == codecIdentity && it["n_order"] == nOrder
                        }

                        val serctorBitCount = s1.countOneBits()
                        val fertBitCount = f1.countOneBits()
                        val currentSector = devScheduling?.get("sector") as? Int ?: 0
                        val currentFertiActivated = s1 == f1

                        val title = scheduling?.get("name")?.toString() ?: "Agendamento $schedulingIdx"
                        val sdf = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
                        val date = Date(st * 1000)

                        reportList.add(
                            ReportItem(
                                0,
                                0,
                                title,
                                ns,
                                serctorBitCount,
                                currentSector,
                                hf,
                                fertBitCount,
                                currentFertiActivated,
                                bw,
                                hw,
                                wp,
                                date = sdf.format(date),
                                cp == 1
                            )
                        )

                        Log.d("Protobuf", "Codec $codecIdentity - Scheduling: $schedulingIdx")
                    }

                } else if(caseNumber == OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.AUTOMATION_REPORT.number){
                    val packet = try {
                        OutgoingPacketOuterClass.OutgoingPacket.parseFrom(data)
                    } catch (e: InvalidProtocolBufferException) {
                        Log.e("Protobuf", "Failed to parse packet", e)
                        continue
                    }

                    val autoReport = packet.automationReport
                    val dataList = autoReport.dataList

                    dataList.forEach { entry ->
                        val st = entry.startTime
                        val sdf = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
                        val date = Date(st * 1_000L)
                        val title = "Automação – $codecIdentity"

                        reportList.add(ReportItem(
                            0,
                            1,
                            title,
                            0,
                            0,
                            0,
                            false,
                            0,
                            false,
                            0,
                            false,
                            false,
                            sdf.format(date),
                            false,
                        ))
                    }

                }
            }

            if (reportList.isNotEmpty()) { // Verifica se a lista de relatórios não está vazia
                binding.fragReportLinearLayoutReport.visibility = View.VISIBLE
            } else {
                binding.fragReportLinearLayoutReport.visibility = View.GONE
            }

            adapter.notifyDataSetChanged()

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun onItemClick(Item: ReportItem) {

    }

    override fun onDestroyView() {
        super.onDestroyView()
        job?.cancel()
    }


}