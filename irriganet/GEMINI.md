# Gemini Code-along Agent Context

This document provides a comprehensive overview of the IrrigaNet Android project, intended to be used as a foundational context for the Gemini code-along agent.

## Project Overview

**IrrigaNet** is an Android application developed in Kotlin for controlling and monitoring agricultural irrigation systems. It functions as an Internet of Things (IoT) client, communicating with irrigation hardware to manage devices like valves, pumps, and sensors.

### Core Technologies

*   **Language:** Kotlin
*   **Platform:** Android
*   **Build Tool:** Gradle
*   **Communication Protocol:** MQTT (using the `hivemq-mqtt-client` library) for real-time communication with hardware.
*   **Data Serialization:** Protocol Buffers (`protobuf`) for structured data exchange, likely with the backend or directly with the IoT devices. It also uses `gson` for JSON processing.
*   **Database:** The application uses a local SQLite database, managed through a custom `DBHelper.kt` class, to store configuration, device information, and other application data.
*   **UI:** The user interface is built with standard Android UI components, including `ViewBinding`, `RecyclerView`, and the Jetpack Navigation component.
*   **Asynchronous Operations:** Kotlin Coroutines are used for managing background threads and asynchronous tasks, such as network requests and database access.

### Architecture

The application follows a fairly standard Android architecture:

*   **UI Layer:** Composed of Fragments (e.g., `DeviceRegistrationFragment`, `MeshDeviceConfigFragment`) that manage the user interface and interactions.
*   **Data Layer:** A `DBHelper.kt` class encapsulates all SQLite database operations. This class is central to the application's data persistence strategy.
*   **Communication:** An MQTT client handles the real-time communication with the irrigation hardware.
*   **Device Model:** The application has a concept of "Mesh Devices," which are physical hardware units that can contain multiple logical "child" devices. The process of adding and configuring these devices is a core feature of the application.

## Building and Running

### Building the Project

The project can be built using Gradle. From the root directory, run the following command:

```bash
./gradlew build
```

### Running the Application

To build and install the application on a connected Android device or emulator, use the following command:

```bash
./gradlew installDebug
```

Alternatively, the project can be opened in Android Studio, which will provide a more user-friendly interface for building, running, and debugging the application.

### Testing

The project includes unit tests and Android instrumentation tests. To run the tests, you can use the following Gradle commands:

*   **Unit Tests:**
    ```bash
    ./gradlew test
    ```
*   **Instrumentation Tests:**
    ```bash
    ./gradlew connectedAndroidTest
    ```

## Development Conventions

*   **Database Management:** All database interactions are centralized in the `DBHelper.kt` file. When adding or modifying database functionality, this is the primary file to edit.
*   **Device Configuration:** The process of adding a new "Mesh Device" is well-documented in `MESH_DEVICE_INSERT.md`. This document should be consulted when working on features related to device management.
*   **Data Synchronization:** The application uses a `dataUpdated` flag in `SharedPreferences` to signal when the database has been modified. This triggers a data reorganization process in `MainActivity.kt`, which is crucial for maintaining data consistency.
*   **Code Style:** The codebase is written in idiomatic Kotlin. New code should follow the existing style and conventions.
*   **Error Handling:** The `MESH_DEVICE_INSERT.md` document suggests that there may be issues with manual database connection management (`db.close()`). Care should be taken to avoid this anti-pattern and instead rely on the `SQLiteOpenHelper` to manage the database lifecycle.
