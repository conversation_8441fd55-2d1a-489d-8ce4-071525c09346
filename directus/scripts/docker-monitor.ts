#!/usr/bin/env bun

/**
 * Docker Monitor Script
 *
 * This script monitors docker-compose output and triggers seed population
 * when "INFO: Server started" is detected. It also allows re-running the
 * docker:down-up command by pressing 'r' and enter.
 */

// Import Bun's built-in APIs
const { spawn, stdin } = Bun;
const { stdout, stderr } = process;

/**
 * Executes the docker:down-up command and monitors its output
 */
async function runDockerDownUpAndMonitor(): Promise<void> {
  stdout.write("Starting docker:down-up command...\n");

  // Execute bun run docker:down-up
  const process = spawn({
    cmd: ["bun", "run", "docker:down-up"],
    stdout: "pipe",
    stderr: "pipe",
    stdin: "ignore",
  });

  let serverStarted = false;

  // Handle stdout
  if (process.stdout) {
    const stdoutReader = process.stdout.getReader();
    const decoder = new TextDecoder();

    (async () => {
      try {
        while (true) {
          const { done, value } = await stdoutReader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          stdout.write(chunk);

          // Check for "INFO: Server started" in the output
          if (chunk.includes("INFO: Server started") && !serverStarted) {
            serverStarted = true;
            stdout.write(
              "\n🚀 Detected 'INFO: Server started' - Running seed:populate in parallel...\n"
            );
            runSeedPopulate();
          }
        }
      } catch (error) {
        stderr.write(`Error reading stdout: ${error}\n`);
      }
    })();
  }

  // Handle stderr
  if (process.stderr) {
    const stderrReader = process.stderr.getReader();
    const decoder = new TextDecoder();

    (async () => {
      try {
        while (true) {
          const { done, value } = await stderrReader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          stderr.write(chunk);

          // Also check stderr for the server started message
          if (chunk.includes("INFO: Server started") && !serverStarted) {
            serverStarted = true;
            stderr.write(
              "\n🚀 Detected 'INFO: Server started' - Running seed:populate in parallel...\n"
            );
            runSeedPopulate();
          }
        }
      } catch (error) {
        stderr.write(`Error reading stderr: ${error}\n`);
      }
    })();
  }

  // Wait for the process to complete
  const exitCode = await process.exited;
  stdout.write(`\nDocker down process completed with exit code: ${exitCode}\n`);
}

/**
 * Runs the seed:populate command in parallel
 */
async function runSeedPopulate(): Promise<void> {
  try {
    stdout.write("Starting seed:populate command...\n");

    const process = spawn({
      cmd: ["bun", "run", "seed:populate"],
      stdout: "pipe",
      stderr: "pipe",
      stdin: "ignore",
    });

    // Handle stdout
    if (process.stdout) {
      const stdoutReader = process.stdout.getReader();
      const decoder = new TextDecoder();

      (async () => {
        try {
          while (true) {
            const { done, value } = await stdoutReader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            stdout.write(`[SEED] ${chunk}`);
          }
        } catch (error) {
          stderr.write(`Error reading seed stdout: ${error}\n`);
        }
      })();
    }

    // Handle stderr
    if (process.stderr) {
      const stderrReader = process.stderr.getReader();
      const decoder = new TextDecoder();

      (async () => {
        try {
          while (true) {
            const { done, value } = await stderrReader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            stderr.write(`[SEED STDERR] ${chunk}`);
          }
        } catch (error) {
          stderr.write(`Error reading seed stderr: ${error}\n`);
        }
      })();
    }

    const exitCode = await process.exited;
    stdout.write(
      `\nSeed populate process completed with exit code: ${exitCode}\n`
    );
  } catch (error) {
    stderr.write(`Error running seed:populate: ${error}\n`);
  }
}

/**
 * Sets up keyboard input listener for 'r' key
 */
function setupKeyboardListener(): void {
  stdout.write("\n📝 Press 'r' and Enter to re-run docker:down-up command\n");

  // Create a readable stream for stdin
  const stdinStream = stdin.stream();
  const reader = stdinStream.getReader();
  const decoder = new TextDecoder();

  (async () => {
    try {
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        buffer += chunk;

        // Check for complete lines (ending with newline)
        const lines = buffer.split("\n");
        buffer = lines.pop() || ""; // Keep incomplete line in buffer

        for (const line of lines) {
          const trimmedLine = line.trim();
          if (trimmedLine === "r" || trimmedLine === "R") {
            stdout.write(
              "\n🔄 'r' pressed - Re-running docker:down-up command...\n"
            );
            // Run in parallel to avoid blocking the stdin reader
            runDockerDownUpAndMonitor()
              .then(() => {
                stdout.write(
                  "\n📝 Press 'r' and Enter to re-run docker:down-up command\n"
                );
              })
              .catch((error) => {
                stderr.write(`Error re-running docker command: ${error}\n`);
              });
          }
        }
      }
    } catch (error) {
      stderr.write(`Error reading stdin: ${error}\n`);
    }
  })();
}

/**
 * Main function
 */
async function main(): Promise<void> {
  stdout.write("🐳 Docker Monitor Script Started\n");
  stdout.write("===============================\n\n");

  // Set up keyboard listener
  setupKeyboardListener();

  // Run the initial docker:down-up command
  await runDockerDownUpAndMonitor();

  stdout.write("\n✅ Initial docker:down-up command completed\n");
  stdout.write("📝 Press 'r' and Enter to re-run docker:down-up command\n");

  // Keep the script running to listen for keyboard input
  // The script will continue running due to the stdin reader
}

/**
 * Spawn process `bun run docker:down` in order to stop the containers
 */
async function runDockerDown(): Promise<void> {
  stdout.write("\nGracefully shutting down Docker containers...\n");
  try {
    const process = spawn({
      cmd: ["bun", "run", "docker:down"],
      stdout: "pipe",
      stderr: "pipe",
      stdin: "ignore",
    });

    // Handle stdout
    if (process.stdout) {
      const stdoutReader = process.stdout.getReader();
      const decoder = new TextDecoder();
      (async () => {
        try {
          while (true) {
            const { done, value } = await stdoutReader.read();
            if (done) break;
            stdout.write(decoder.decode(value));
          }
        } catch (error) {
          stderr.write(`Error reading docker:down stdout: ${error}\n`);
        }
      })();
    }

    // Handle stderr
    if (process.stderr) {
      const stderrReader = process.stderr.getReader();
      const decoder = new TextDecoder();
      (async () => {
        try {
          while (true) {
            const { done, value } = await stderrReader.read();
            if (done) break;
            stderr.write(decoder.decode(value));
          }
        } catch (error) {
          stderr.write(`Error reading docker:down stderr: ${error}\n`);
        }
      })();
    }

    const exitCode = await process.exited;
    stdout.write(
      `\nDocker down process completed with exit code: ${exitCode}\n`
    );
  } catch (error) {
    stderr.write(`Error running docker:down: ${error}\n`);
  }
}

// Graceful shutdown handler
const shutdown = async () => {
  stdout.write("\n\nCaught interrupt signal. Shutting down...\n");
  await runDockerDown();
  // The stdin reader keeps the process alive, so we need to exit explicitly.
  process.exit(0);
};

// Listen for termination signals (e.g., Ctrl+C)
process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);

// Run the main function
main().catch(async (error) => {
  stderr.write(`Fatal error: ${error}\n`);
  await runDockerDown().finally(() => {
    process.exit(1);
  });
});
