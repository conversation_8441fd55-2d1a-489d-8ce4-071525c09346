#!/usr/bin/env bun

/**
 * Docker Directus Recreate Loop Script
 *
 * This script executes `docker compose up --build`, watches for "INFO: Server started",
 * runs `bun run seed:populate` in parallel when detected, listens for 'r' key to run
 * `docker:directus:recreate`, and properly shuts down containers on exit.
 */

// Import Bun's built-in APIs
const { spawn, stdin } = Bun;
const { stdout, stderr } = process;

// Keep track of the main docker process
let mainDockerProcess: any = null;
let isRecreating = false;

/**
 * Executes docker compose up --build and monitors its output
 */
async function runDockerComposeUpAndMonitor(): Promise<void> {
  stdout.write("Starting docker compose up --build...\n");

  // Execute docker compose up --build
  mainDockerProcess = spawn({
    cmd: ["docker", "compose", "up", "--build"],
    stdout: "pipe",
    stderr: "pipe",
    stdin: "ignore",
  });

  let serverStarted = false;

  // Handle stdout
  if (
    mainDockerProcess.stdout &&
    typeof mainDockerProcess.stdout !== "number"
  ) {
    const stdoutReader = mainDockerProcess.stdout.getReader();
    const decoder = new TextDecoder();

    (async () => {
      try {
        while (true) {
          const { done, value } = await stdoutReader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          stdout.write(chunk);

          // Check for "INFO: Server started" in the output
          if (chunk.includes("INFO: Server started") && !isRecreating) {
            serverStarted = true;
            stdout.write(
              "\n🚀 Detected 'INFO: Server started' - Running seed:populate in parallel...\n"
            );
            runSeedPopulate();
          }
        }
      } catch (error) {
        stderr.write(`Error reading stdout: ${error}\n`);
      }
    })();
  }

  // Handle stderr
  if (
    mainDockerProcess.stderr &&
    typeof mainDockerProcess.stderr !== "number"
  ) {
    const stderrReader = mainDockerProcess.stderr.getReader();
    const decoder = new TextDecoder();

    (async () => {
      try {
        while (true) {
          const { done, value } = await stderrReader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          stderr.write(chunk);

          // Also check stderr for the server started message
          if (chunk.includes("INFO: Server started") && !isRecreating) {
            serverStarted = true;
            stderr.write(
              "\n🚀 Detected 'INFO: Server started' - Running seed:populate in parallel...\n"
            );
            runSeedPopulate();
          }
        }
      } catch (error) {
        stderr.write(`Error reading stderr: ${error}\n`);
      }
    })();
  }

  // Wait for the process to complete
  const exitCode = await mainDockerProcess.exited;
  stdout.write(
    `\nDocker compose up process completed with exit code: ${exitCode}\n`
  );
}

/**
 * Runs the seed:populate command in parallel
 */
async function runSeedPopulate(): Promise<void> {
  try {
    stdout.write("Starting seed:populate command...\n");

    const process = spawn({
      cmd: ["bun", "run", "seed:populate"],
      stdout: "pipe",
      stderr: "pipe",
      stdin: "ignore",
    });

    // Handle stdout
    if (process.stdout && typeof process.stdout !== "number") {
      const stdoutReader = process.stdout.getReader();
      const decoder = new TextDecoder();

      (async () => {
        try {
          while (true) {
            const { done, value } = await stdoutReader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            stdout.write(`[SEED] ${chunk}`);
          }
        } catch (error) {
          stderr.write(`Error reading seed stdout: ${error}\n`);
        }
      })();
    }

    // Handle stderr
    if (process.stderr && typeof process.stderr !== "number") {
      const stderrReader = process.stderr.getReader();
      const decoder = new TextDecoder();

      (async () => {
        try {
          while (true) {
            const { done, value } = await stderrReader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            stderr.write(`[SEED STDERR] ${chunk}`);
          }
        } catch (error) {
          stderr.write(`Error reading seed stderr: ${error}\n`);
        }
      })();
    }

    const exitCode = await process.exited;
    stdout.write(
      `\nSeed populate process completed with exit code: ${exitCode}\n`
    );
  } catch (error) {
    stderr.write(`Error running seed:populate: ${error}\n`);
  }
}

/**
 * Runs docker:directus:recreate command
 */
async function runDockerDirectusRecreate(): Promise<void> {
  try {
    isRecreating = true;
    stdout.write("\n🔄 Running docker:directus:recreate...\n");

    const process = spawn({
      cmd: ["bun", "run", "docker:directus:recreate"],
      stdout: "pipe",
      stderr: "pipe",
      stdin: "ignore",
    });

    // Handle stdout
    if (process.stdout && typeof process.stdout !== "number") {
      const stdoutReader = process.stdout.getReader();
      const decoder = new TextDecoder();

      (async () => {
        try {
          while (true) {
            const { done, value } = await stdoutReader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            stdout.write(`[RECREATE] ${chunk}`);
          }
        } catch (error) {
          stderr.write(`Error reading recreate stdout: ${error}\n`);
        }
      })();
    }

    // Handle stderr
    if (process.stderr && typeof process.stderr !== "number") {
      const stderrReader = process.stderr.getReader();
      const decoder = new TextDecoder();

      (async () => {
        try {
          while (true) {
            const { done, value } = await stderrReader.read();
            if (done) break;

            const chunk = decoder.decode(value);
            stderr.write(`[RECREATE STDERR] ${chunk}`);
          }
        } catch (error) {
          stderr.write(`Error reading recreate stderr: ${error}\n`);
        }
      })();
    }

    const exitCode = await process.exited;
    stdout.write(
      `\nDocker directus recreate process completed with exit code: ${exitCode}\n`
    );
    isRecreating = false;
  } catch (error) {
    stderr.write(`Error running docker:directus:recreate: ${error}\n`);
    isRecreating = false;
  }
}

/**
 * Sets up keyboard input listener for 'r' key
 */
function setupKeyboardListener(): void {
  stdout.write("\n📝 Press 'r' and Enter to run docker:directus:recreate\n");

  // Create a readable stream for stdin
  const stdinStream = stdin.stream();
  const reader = stdinStream.getReader();
  const decoder = new TextDecoder();

  (async () => {
    try {
      let buffer = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        buffer += chunk;

        // Check for complete lines (ending with newline)
        const lines = buffer.split("\n");
        buffer = lines.pop() || ""; // Keep incomplete line in buffer

        for (const line of lines) {
          const trimmedLine = line.trim();
          if (trimmedLine === "r" || trimmedLine === "R") {
            stdout.write(
              "\n🔄 'r' pressed - Running docker:directus:recreate...\n"
            );
            // Run in parallel to avoid blocking the stdin reader
            runDockerDirectusRecreate()
              .then(() => {
                stdout.write(
                  "\n📝 Press 'r' and Enter to run docker:directus:recreate\n"
                );
              })
              .catch((error) => {
                stderr.write(
                  `Error running docker:directus:recreate: ${error}\n`
                );
              });
          }
        }
      }
    } catch (error) {
      stderr.write(`Error reading stdin: ${error}\n`);
    }
  })();
}

/**
 * Stops docker containers gracefully
 */
async function stopDockerContainers(): Promise<void> {
  stdout.write("\nGracefully stopping Docker containers...\n");
  try {
    const process = spawn({
      cmd: ["docker", "compose", "stop", "--timeout", "5"],
      stdout: "pipe",
      stderr: "pipe",
      stdin: "ignore",
    });

    // Handle stdout
    if (process.stdout && typeof process.stdout !== "number") {
      const stdoutReader = process.stdout.getReader();
      const decoder = new TextDecoder();
      (async () => {
        try {
          while (true) {
            const { done, value } = await stdoutReader.read();
            if (done) break;
            stdout.write(decoder.decode(value));
          }
        } catch (error) {
          stderr.write(`Error reading docker stop stdout: ${error}\n`);
        }
      })();
    }

    // Handle stderr
    if (process.stderr && typeof process.stderr !== "number") {
      const stderrReader = process.stderr.getReader();
      const decoder = new TextDecoder();
      (async () => {
        try {
          while (true) {
            const { done, value } = await stderrReader.read();
            if (done) break;
            stderr.write(decoder.decode(value));
          }
        } catch (error) {
          stderr.write(`Error reading docker stop stderr: ${error}\n`);
        }
      })();
    }

    const exitCode = await process.exited;
    stdout.write(
      `\nDocker stop process completed with exit code: ${exitCode}\n`
    );
  } catch (error) {
    stderr.write(`Error running docker stop: ${error}\n`);
  }
}

/**
 * Main function
 */
async function main(): Promise<void> {
  stdout.write("🐳 Docker Directus Recreate Loop Script Started\n");
  stdout.write("==============================================\n\n");

  // Set up keyboard listener
  setupKeyboardListener();

  // Run the initial docker compose up --build command
  await runDockerComposeUpAndMonitor();

  stdout.write("\n✅ Docker compose up command completed\n");
  stdout.write("📝 Press 'r' and Enter to run docker:directus:recreate\n");

  // Keep the script running to listen for keyboard input
  // The script will continue running due to the stdin reader
}

// Graceful shutdown handler
const shutdown = async () => {
  stdout.write("\n\nCaught interrupt signal. Shutting down...\n");
  await stopDockerContainers();
  // The stdin reader keeps the process alive, so we need to exit explicitly.
  process.exit(0);
};

// Listen for termination signals (e.g., Ctrl+C)
process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);

// Run the main function
main().catch(async (error) => {
  stderr.write(`Fatal error: ${error}\n`);
  await stopDockerContainers().finally(() => {
    process.exit(1);
  });
});
