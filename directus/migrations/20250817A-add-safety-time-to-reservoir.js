/**
 * This migration file adds safety_time field to the reservoir table.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.alterTable("reservoir", (table) => {
      table
        .integer("safety_time_minutes")
        .nullable()
        .comment("Safety time in minutes for pump operation when reservoir is being refilled");
    });
  });
}

/**
 * This migration file drops safety_time field from the reservoir table.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.alterTable("reservoir", (table) => {
      table.dropColumn("safety_time_minutes");
    });
  });
}