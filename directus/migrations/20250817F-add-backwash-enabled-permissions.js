/**
 * Migration to re-add backwash_enabled field to irrigation_plan permissions
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Update permissions to include backwash_enabled in create and update actions
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes,fertigation_enabled,backwash_enabled",
      });

    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes,fertigation_enabled,backwash_enabled",
      });
  });
}

/**
 * Migration to remove backwash_enabled field from irrigation_plan permissions
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Revert permissions to exclude backwash_enabled
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes,fertigation_enabled",
      });

    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes,fertigation_enabled",
      });
  });
}