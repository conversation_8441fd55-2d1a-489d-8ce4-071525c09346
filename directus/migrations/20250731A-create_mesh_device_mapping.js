/**
 * Migration to create mesh_device_mapping table, trigger/functions, and add current_mesh_device_mapping to property_device.
 * Implements business rules and scheduler function per TASKS_250731_1.md.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // 1) Create mesh_device_mapping table
    await tx.schema.createTable("mesh_device_mapping", (table) => {
      table
        .uuid("id")
        .primary()
        .notNullable()
        .defaultTo(tx.raw("gen_random_uuid()"));
      table
        .uuid("mesh_property_device")
        .notNullable()
        .references("id")
        .inTable("property_device")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .uuid("lic_property_device")
        .notNullable()
        .references("id")
        .inTable("property_device")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.timestamp("start_date", { useTz: true }).notNullable();
      table.timestamp("end_date", { useTz: true }).nullable();
      table
        .timestamp("date_created", { useTz: true })
        .nullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_created")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .timestamp("date_updated", { useTz: true })
        .nullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_updated")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");

      table.index("mesh_property_device");
      table.index("lic_property_device");
      table.index(["mesh_property_device", "start_date"]);
    });

    // 2) Trigger to maintain date_updated
    await tx.raw(`
      CREATE TRIGGER set_mesh_device_mapping_date_updated
      BEFORE UPDATE ON mesh_device_mapping
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp_column();
    `);

    // 3) Add column to property_device to hold current active mapping
    const hasCol = await tx.schema.hasColumn(
      "property_device",
      "current_mesh_device_mapping"
    );
    if (!hasCol) {
      await tx.schema.alterTable("property_device", (table) => {
        table
          .uuid("current_mesh_device_mapping")
          .nullable()
          .references("id")
          .inTable("mesh_device_mapping")
          .onDelete("SET NULL")
          .onUpdate("RESTRICT");
      });
      await tx.raw(
        `CREATE INDEX IF NOT EXISTS property_device_current_mesh_idx ON property_device (current_mesh_device_mapping);`
      );
    }

    // 4) Function: im_update_current_mesh_device_mapping(mesh_ids uuid[], reference_date timestamptz default now())
    await tx.raw(`
      CREATE OR REPLACE FUNCTION im_update_current_mesh_device_mapping(mesh_ids uuid[], reference_date timestamptz DEFAULT now())
      RETURNS void
      LANGUAGE plpgsql
      AS $$
      DECLARE
        mesh_id uuid;
        active_mapping_id uuid;
      BEGIN
        IF mesh_ids IS NULL OR array_length(mesh_ids, 1) IS NULL THEN
          RETURN;
        END IF;

        FOREACH mesh_id IN ARRAY mesh_ids LOOP
          SELECT mdm.id
            INTO active_mapping_id
          FROM mesh_device_mapping mdm
          WHERE mdm.mesh_property_device = mesh_id
            AND mdm.start_date <= reference_date
            AND (mdm.end_date IS NULL OR reference_date <= mdm.end_date)
          ORDER BY mdm.start_date DESC
          LIMIT 1;

          UPDATE property_device pd
            SET current_mesh_device_mapping = active_mapping_id
          WHERE pd.id = mesh_id;
        END LOOP;
      END;
      $$;
    `);

    // 5) Trigger function to enforce constraints and accommodation logic
    await tx.raw(`
      CREATE OR REPLACE FUNCTION trg_mesh_device_mapping_enforce()
      RETURNS trigger
      LANGUAGE plpgsql
      AS $$
      DECLARE
        v_mesh_property uuid;
        v_lic_property uuid;
        v_mesh_device uuid;
        v_lic_device uuid;
        v_mesh_model text;
        v_lic_model text;
        ref_end timestamptz;
        r RECORD;
        orig_end timestamptz;
      BEGIN
        -- Normalize NEW.end_date for comparisons
        ref_end := COALESCE(NEW.end_date, 'infinity'::timestamptz);

        -- Basic sanity: mapping must not be self-mapping
        IF NEW.mesh_property_device = NEW.lic_property_device THEN
          RAISE EXCEPTION 'mesh_property_device must be different from lic_property_device';
        END IF;

        -- Same property check and capture device ids
        SELECT pd.property, pd.device INTO v_mesh_property, v_mesh_device
        FROM property_device pd WHERE pd.id = NEW.mesh_property_device;
        IF v_mesh_property IS NULL THEN
          RAISE EXCEPTION 'mesh_property_device % does not exist', NEW.mesh_property_device;
        END IF;

        SELECT pd.property, pd.device INTO v_lic_property, v_lic_device
        FROM property_device pd WHERE pd.id = NEW.lic_property_device;
        IF v_lic_property IS NULL THEN
          RAISE EXCEPTION 'lic_property_device % does not exist', NEW.lic_property_device;
        END IF;

        IF v_mesh_property <> v_lic_property THEN
          RAISE EXCEPTION 'mesh and LIC property_device must belong to the same property';
        END IF;

        -- Device models: mesh must be WPC-PL10, WPC-PL50, VC, or RM; LIC must be LIC
        SELECT d.model INTO v_mesh_model FROM device d WHERE d.id = v_mesh_device;
        SELECT d.model INTO v_lic_model FROM device d WHERE d.id = v_lic_device;

        IF v_lic_model <> 'LIC' THEN
          RAISE EXCEPTION 'lic_property_device must reference a device with model=LIC';
        END IF;

        IF v_mesh_model NOT IN ('WPC-PL10', 'WPC-PL50', 'VC', 'RM') THEN
          RAISE EXCEPTION 'mesh_property_device must reference a mesh-capable device (WPC-PL10, WPC-PL50, VC, RM)';
        END IF;

        -- Devices active at association time: ensure both PD rows contains mesh_device_mapping (NEW) period
        -- Mesh PD must fully contain the mesh_device_mapping period [NEW.start_date, NEW.end_date]
        PERFORM 1 FROM property_device pd
         WHERE pd.id = NEW.mesh_property_device
           AND pd.start_date <= NEW.start_date
           AND COALESCE(pd.end_date, 'infinity'::timestamptz) >= COALESCE(NEW.end_date, 'infinity'::timestamptz);

        IF NOT FOUND THEN
          RAISE EXCEPTION 'mesh_device_mapping period must be contained within the mesh_property_device period';
        END IF;

        -- LIC PD must fully contain the mesh_device_mapping period [NEW.start_date, NEW.end_date]
        PERFORM 1 FROM property_device pd
         WHERE pd.id = NEW.lic_property_device
           AND pd.start_date <= NEW.start_date
           AND COALESCE(pd.end_date, 'infinity'::timestamptz) >= COALESCE(NEW.end_date, 'infinity'::timestamptz);
        IF NOT FOUND THEN
          RAISE EXCEPTION 'mesh_device_mapping period must be contained within the lic_property_device period';
        END IF;

        -- Accommodation logic for overlapping mappings for same mesh_property_device
        -- Loop over overlapping existing rows
        FOR r IN
          SELECT *
          FROM mesh_device_mapping m
          WHERE m.mesh_property_device = NEW.mesh_property_device
            AND m.id <> COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid)
            AND NOT (
              COALESCE(m.end_date, 'infinity'::timestamptz) <= NEW.start_date
              OR ref_end <= COALESCE(m.start_date, '-infinity'::timestamptz)
            )
          ORDER BY m.start_date
        LOOP
          orig_end := r.end_date;

          -- Case 3: existing entirely contained within new -> error
          IF r.start_date >= NEW.start_date AND (COALESCE(r.end_date, 'infinity'::timestamptz) <= ref_end) THEN
            RAISE EXCEPTION 'There is already an association active in the requested period';
          END IF;

          -- Case 4: existing contains new -> split existing into two
          IF r.start_date < NEW.start_date AND (COALESCE(r.end_date, 'infinity'::timestamptz) > ref_end) THEN
            -- Left piece: set end to NEW.start_date
            UPDATE mesh_device_mapping
              SET end_date = NEW.start_date
            WHERE id = r.id;

            -- Right piece: create a new row starting at NEW.end_date + 1s up to original end
            IF NEW.end_date IS NULL THEN
              -- new has no end; right piece would be empty; skip creation
            ELSE
              INSERT INTO mesh_device_mapping(
                id, mesh_property_device, lic_property_device, start_date, end_date, date_created, user_created, date_updated, user_updated
              ) VALUES (
                gen_random_uuid(), r.mesh_property_device, r.lic_property_device, NEW.end_date + interval '1 second', orig_end, now(), NULL, now(), NULL
              );
              -- NOTE: Future work: If there are FKs pointing to r.id over specific periods, evaluate and update them accordingly.
            END IF;

          -- Case 1: existing starts before new starts -> truncate existing.end_date to NEW.start_date
          ELSIF r.start_date < NEW.start_date THEN
            UPDATE mesh_device_mapping
              SET end_date = NEW.start_date
            WHERE id = r.id;

          -- Case 2: existing ends after new ends -> move existing.start_date to NEW.end_date + 1s
          ELSIF COALESCE(r.end_date, 'infinity'::timestamptz) > ref_end THEN
            IF NEW.end_date IS NULL THEN
              -- new has no end; thus existing cannot remain; move start after infinity is meaningless.
              -- In this scenario, existing is "to the right" but new is infinite; treat as contained -> handled above.
              RAISE EXCEPTION 'There is already an association active in the requested period';
            ELSE
              UPDATE mesh_device_mapping
                SET start_date = NEW.end_date + interval '1 second'
              WHERE id = r.id;
            END IF;
          END IF;
        END LOOP;

        -- After accommodation, ensure a mesh has only one LIC at a time for the new record window
        -- Overlaps still present here indicate logic error
        PERFORM 1
        FROM mesh_device_mapping m
        WHERE m.mesh_property_device = NEW.mesh_property_device
          AND m.id <> COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid)
          AND NOT (
            COALESCE(m.end_date, 'infinity'::timestamptz) <= NEW.start_date
            OR ref_end <= COALESCE(m.start_date, '-infinity'::timestamptz)
          );
        IF FOUND THEN
          RAISE EXCEPTION 'mesh_device_mapping records must not overlap for the same mesh_property_device';
        END IF;

        -- Do NOT call updater here; final image not persisted yet in BEFORE trigger.
        RETURN NEW;
      END;
      $$;
    `);

    // 6) Create triggers for INSERT/UPDATE to call the enforcement function
    await tx.raw(`
      CREATE TRIGGER trg_mesh_device_mapping_insert
      BEFORE INSERT ON mesh_device_mapping
      FOR EACH ROW
      EXECUTE FUNCTION trg_mesh_device_mapping_enforce();
    `);
    await tx.raw(`
      CREATE TRIGGER trg_mesh_device_mapping_update
      BEFORE UPDATE OF mesh_property_device, lic_property_device, start_date, end_date ON mesh_device_mapping
      FOR EACH ROW
      EXECUTE FUNCTION trg_mesh_device_mapping_enforce();
    `);

    // 6.1) Create AFTER trigger to update current mapping once final row image is persisted
    await tx.raw(`
      CREATE OR REPLACE FUNCTION trg_mesh_device_mapping_after_recalc()
      RETURNS trigger
      LANGUAGE plpgsql
      AS $$
      BEGIN
        PERFORM im_update_current_mesh_device_mapping(ARRAY[NEW.mesh_property_device], now());
        RETURN NEW;
      END;
      $$;
    `);
    await tx.raw(`
      CREATE TRIGGER trg_mesh_device_mapping_after
      AFTER INSERT OR UPDATE ON mesh_device_mapping
      FOR EACH ROW
      EXECUTE FUNCTION trg_mesh_device_mapping_after_recalc();
    `);

    // 7) Helpful indexes for interval queries
    await tx.raw(`
      CREATE INDEX IF NOT EXISTS mesh_device_mapping_mesh_start_idx
        ON mesh_device_mapping (mesh_property_device, start_date);
    `);
    await tx.raw(`
      CREATE INDEX IF NOT EXISTS mesh_device_mapping_mesh_end_idx
        ON mesh_device_mapping (mesh_property_device, end_date);
    `);
  });
}

export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Drop triggers
    await tx.raw(
      "DROP TRIGGER IF EXISTS trg_mesh_device_mapping_update ON mesh_device_mapping;"
    );
    await tx.raw(
      "DROP TRIGGER IF EXISTS trg_mesh_device_mapping_insert ON mesh_device_mapping;"
    );
    await tx.raw(
      "DROP TRIGGER IF EXISTS set_mesh_device_mapping_date_updated ON mesh_device_mapping;"
    );

    // Drop AFTER trigger
    await tx.raw(
      "DROP TRIGGER IF EXISTS trg_mesh_device_mapping_after ON mesh_device_mapping;"
    );

    // Drop functions
    await tx.raw(
      "DROP FUNCTION IF EXISTS trg_mesh_device_mapping_after_recalc();"
    );
    await tx.raw("DROP FUNCTION IF EXISTS trg_mesh_device_mapping_enforce();");
    await tx.raw(
      "DROP FUNCTION IF EXISTS im_update_current_mesh_device_mapping(uuid[], timestamptz);"
    );

    // Remove column from property_device if exists
    const hasCol = await tx.schema.hasColumn(
      "property_device",
      "current_mesh_device_mapping"
    );
    if (hasCol) {
      await tx.schema.alterTable("property_device", (table) => {
        table.dropColumn("current_mesh_device_mapping");
      });
    }

    // Drop table
    await tx.schema.dropTableIfExists("mesh_device_mapping");
  });
}
