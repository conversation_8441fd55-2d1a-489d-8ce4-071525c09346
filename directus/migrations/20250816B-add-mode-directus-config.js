/**
 * Migration to add mode field configuration to Directus
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Add mode field configuration to directus_fields
    await tx.batchInsert("directus_fields", [
      {
        collection: "water_pump",
        field: "mode",
        special: null,
        interface: "select-dropdown",
        options: JSON.stringify({
          choices: [
            { text: "Pulse", value: "PULSE" },
            { text: "Continuous", value: "CONTINUOUS" }
          ]
        }),
        display: "labels",
        display_options: JSON.stringify({
          choices: [
            { text: "Pulse", value: "PULSE" },
            { text: "Continuous", value: "CONTINUOUS" }
          ]
        }),
        readonly: false,
        hidden: false,
        sort: 13, // After flow_rate_lh (sort: 12)
        width: "half",
        translations: null,
        note: "Operating mode of the water pump",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // Update sort order for fields after mode
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "date_created" })
      .update({ sort: 14 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "date_updated" })
      .update({ sort: 15 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "user_created" })
      .update({ sort: 16 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "user_updated" })
      .update({ sort: 17 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "notes" })
      .update({ sort: 18 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "metadata" })
      .update({ sort: 19 });

    // Update permissions to include mode in create and update actions
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "identifier,label,pump_type,pump_model,has_frequency_inverter,monitor_operation,property,water_pump_controller,notes,metadata,flow_rate_lh,mode",
      });

    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "identifier,label,pump_type,pump_model,has_frequency_inverter,monitor_operation,property,water_pump_controller,notes,metadata,flow_rate_lh,mode",
      });
  });
}

/**
 * Migration to remove mode field configuration from Directus
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove mode field configuration
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "mode" })
      .del();

    // Revert sort order for fields after mode
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "date_created" })
      .update({ sort: 13 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "date_updated" })
      .update({ sort: 15 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "user_created" })
      .update({ sort: 14 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "user_updated" })
      .update({ sort: 16 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "notes" })
      .update({ sort: 17 });
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "metadata" })
      .update({ sort: 18 });

    // Revert permissions to exclude mode
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "identifier,label,pump_type,pump_model,has_frequency_inverter,monitor_operation,property,water_pump_controller,notes,metadata,flow_rate_lh",
      });

    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "identifier,label,pump_type,pump_model,has_frequency_inverter,monitor_operation,property,water_pump_controller,notes,metadata,flow_rate_lh",
      });
  });
}