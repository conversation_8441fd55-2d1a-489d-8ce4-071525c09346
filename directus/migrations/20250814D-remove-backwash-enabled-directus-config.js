/**
 * Migration to remove backwash_enabled field configuration from Directus
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Remove backwash_enabled field configuration
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "backwash_enabled" })
      .del();

    // Update permissions to exclude backwash_enabled from create and update actions
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes,fertigation_enabled",
      });

    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes,fertigation_enabled",
      });
  });
}

/**
 * Migration to add back backwash_enabled field configuration to Directus
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Add backwash_enabled field configuration back to directus_fields
    await tx.batchInsert("directus_fields", [
      {
        collection: "irrigation_plan",
        field: "backwash_enabled",
        special: "cast-boolean",
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 7,
        width: "half",
        translations: null,
        note: "Indicates if backwash is enabled for this irrigation plan",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // Update permissions to include backwash_enabled back in create and update actions
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes,fertigation_enabled,backwash_enabled",
      });

    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes,fertigation_enabled,backwash_enabled",
      });
  });
}