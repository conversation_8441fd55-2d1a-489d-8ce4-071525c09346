import { describe, it, beforeEach, afterEach, expect } from "bun:test";
import { Knex } from "knex";
import { createKnex, begin, rollbackAndDestroy } from "./helpers/db";
import { seedDatabase } from "../src/seed/index";

describe("seedDatabase", () => {
  let knex: Knex;
  let trx: Knex.Transaction;

  beforeEach(async () => {
    knex = createKnex();
    trx = await begin(knex);
  });

  afterEach(async () => {
    await rollbackAndDestroy(trx);
  });

  it("should successfully seed the database with all required data", async () => {
    // Execute the seedDatabase function
    await seedDatabase(trx);

    // 1. Verify users were created (including test user)
    const users = await trx("directus_users")
      .whereIn("email", [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ])
      .select("*");

    expect(users).toHaveLength(3);
    expect(users.map((u: any) => u.email).sort()).toEqual([
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
    ]);

    // Verify user details
    const joaquim = users.find(
      (u: any) => u.email === "<EMAIL>"
    );
    const maria = users.find(
      (u: any) => u.email === "<EMAIL>"
    );
    const testUser = users.find((u: any) => u.email === "<EMAIL>");

    expect(joaquim?.first_name).toBe("Joaquim");
    expect(joaquim?.last_name).toBe("Silva");
    expect(maria?.first_name).toBe("Maria");
    expect(maria?.last_name).toBe("Oliveira");
    expect(testUser?.first_name).toBe("Test");
    expect(testUser?.last_name).toBe("User");

    // 2. Verify accounts were created
    const accounts = await trx("account").select("*");
    expect(accounts).toHaveLength(2);

    const joaquimAccount = accounts.find((a: any) => a.owner === joaquim?.id);
    const mariaAccount = accounts.find((a: any) => a.owner === maria?.id);

    expect(joaquimAccount).toBeDefined();
    expect(mariaAccount).toBeDefined();

    // 3. Verify properties were created
    const properties = await trx("property").select("*");
    expect(properties).toHaveLength(3);

    const propertyNames = properties.map((p: any) => p.name).sort();
    expect(propertyNames).toEqual([
      "Chácara das Flores",
      "Fazenda Sol Nascente",
      "Sítio Bela Vista",
    ]);

    // Verify property details for Fazenda Sol Nascente
    const fazenda = properties.find(
      (p: any) => p.name === "Fazenda Sol Nascente"
    );
    expect(fazenda?.account).toBe(joaquimAccount?.id);
    expect(fazenda?.address_postal_code).toBe("12345-678");
    expect(fazenda?.address_city).toBe("Cidade Sol");
    expect(fazenda?.address_state).toBe("SP");
    expect(fazenda?.rain_gauge_enabled).toBe(true);

    // 4. Verify devices were created
    const devices = await trx("device").select("*");
    expect(devices.length).toBeGreaterThan(0);

    // Check for different device models
    const deviceModels = devices.map((d: any) => d.model);
    expect(deviceModels).toContain("LIC");
    expect(deviceModels).toContain("WPC-PL10");
    expect(deviceModels).toContain("WPC-PL50");
    expect(deviceModels).toContain("VC");
    expect(deviceModels).toContain("RM");

    // 5. Verify property_device associations
    const propertyDevices = await trx("property_device").select("*");
    expect(propertyDevices.length).toBeGreaterThan(0);

    // All property devices should have valid start dates
    propertyDevices.forEach((pd: any) => {
      expect(pd.start_date).toBeDefined();
      expect(new Date(pd.start_date)).toBeInstanceOf(Date);
    });

    // 6. Verify projects were created (2 per property)
    const projects = await trx("project").select("*");
    expect(projects).toHaveLength(6); // 3 properties * 2 projects each

    // Each project should have a LIC controller
    const projectsWithLic = await trx("project")
      .whereNotNull("localized_irrigation_controller")
      .select("*");
    expect(projectsWithLic).toHaveLength(6);

    // 7. Verify water pumps were created
    const waterPumps = await trx("water_pump").select("*");
    expect(waterPumps.length).toBeGreaterThan(0);

    const pumpTypes = waterPumps.map((wp: any) => wp.pump_type);
    expect(pumpTypes).toContain("IRRIGATION");
    expect(pumpTypes).toContain("FERTIGATION");

    // 8. Verify sectors were created
    const sectors = await trx("sector").select("*");
    expect(sectors.length).toBeGreaterThan(0);

    // Each sector should have a valve controller
    sectors.forEach((sector: any) => {
      expect(sector.valve_controller).toBeDefined();
      expect(sector.valve_controller_output).toBeGreaterThan(0);
      expect(sector.valve_controller_output).toBeLessThanOrEqual(4);
    });

    // 9. Verify irrigation plans were created
    const irrigationPlans = await trx("irrigation_plan").select("*");
    expect(irrigationPlans.length).toBeGreaterThan(0);

    // Each plan should have steps
    const irrigationSteps = await trx("irrigation_plan_step").select("*");
    expect(irrigationSteps.length).toBeGreaterThan(0);

    // 10. Verify mesh device mappings were created
    const meshMappings = await trx("mesh_device_mapping").select("*");
    expect(meshMappings.length).toBeGreaterThan(0);

    // All mesh mappings should be currently active (end_date is null)
    meshMappings.forEach((mapping: any) => {
      expect(mapping.start_date).toBeDefined();
    });

    // 11. Verify account user relationships were created (including test user)
    const accountUserRelations = await trx("account_user").select("*");
    expect(accountUserRelations.length).toBe(6); // 4 main + 2 for test user

    // Both main users should be associated with both accounts
    const joaquimRelations = accountUserRelations.filter(
      (r: any) => r.user === joaquim?.id
    );
    const mariaRelations = accountUserRelations.filter(
      (r: any) => r.user === maria?.id
    );
    const testUserRelations = accountUserRelations.filter(
      (r: any) => r.user === testUser?.id
    );

    expect(joaquimRelations.length).toBe(2);
    expect(mariaRelations.length).toBe(2);
    expect(testUserRelations.length).toBe(2); // Test user associated with both accounts
  });

  it("should reject seeding when users already exist", async () => {
    // First, seed the database
    await seedDatabase(trx);

    // Attempt to seed again should throw an error
    await expect(seedDatabase(trx)).rejects.toThrow(
      "Seed users already exist."
    );
  });

  it("should create proper device identifiers based on model", async () => {
    await seedDatabase(trx);

    const devices = await trx("device").select("*");

    const licDevices = devices.filter((d: any) => d.model === "LIC");
    const otherDevices = devices.filter((d: any) => d.model !== "LIC");

    // LIC devices should have 12-character identifiers
    licDevices.forEach((device: any) => {
      expect(device.identifier).toHaveLength(12);
      expect(/^[0-9A-F]+$/.test(device.identifier)).toBe(true);
    });

    // Other devices should have 6-character identifiers
    otherDevices.forEach((device: any) => {
      expect(device.identifier).toHaveLength(6);
      expect(/^[0-9A-F]+$/.test(device.identifier)).toBe(true);
    });
  });

  it("should create proper mesh device mappings respecting property constraints", async () => {
    await seedDatabase(trx);

    // Get all mesh device mappings
    const meshMappings = await trx("mesh_device_mapping")
      .join(
        "property_device as mesh_pd",
        "mesh_device_mapping.mesh_property_device",
        "mesh_pd.id"
      )
      .join(
        "property_device as lic_pd",
        "mesh_device_mapping.lic_property_device",
        "lic_pd.id"
      )
      .select(
        "mesh_device_mapping.*",
        "mesh_pd.property as mesh_property",
        "lic_pd.property as lic_property"
      );

    // All mesh mappings should have mesh and LIC devices in the same property
    meshMappings.forEach((mapping: any) => {
      expect(mapping.mesh_property).toBe(mapping.lic_property);
    });
  });

  it("should create irrigation plans with valid time schedules", async () => {
    await seedDatabase(trx);

    const irrigationPlans = await trx("irrigation_plan").select("*");

    irrigationPlans.forEach((plan: any) => {
      // Verify start_time format (should be HH:MM:SS)
      expect(plan.start_time).toMatch(/^\d{2}:\d{2}:\d{2}$/);

      // Verify days_of_week is an array
      expect(Array.isArray(plan.days_of_week)).toBe(true);
      expect(plan.days_of_week.length).toBeGreaterThan(0);

      // All days should be valid
      const validDays = ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"];
      plan.days_of_week.forEach((day: string) => {
        expect(validDays).toContain(day);
      });
    });
  });

  it("should create irrigation steps with proper sequence", async () => {
    await seedDatabase(trx);

    const irrigationPlans = await trx("irrigation_plan").select("id");

    for (const plan of irrigationPlans) {
      const steps = await trx("irrigation_plan_step")
        .where({ irrigation_plan: plan.id })
        .orderBy("order", "asc")
        .select("*");

      expect(steps.length).toBeGreaterThan(0);

      // Verify step order is sequential starting from 1
      steps.forEach((step: any, index: number) => {
        expect(step.order).toBe(index + 1);
        expect(step.duration_seconds).toBeGreaterThan(0);
      });
    }
  });

  it("should create proper property configurations", async () => {
    await seedDatabase(trx);

    const properties = await trx("property").select("*");

    properties.forEach((property: any) => {
      // Verify required fields
      expect(property.name).toBeDefined();
      expect(property.account).toBeDefined();
      expect(property.timezone).toBe("America/Sao_Paulo");

      // Verify backwash configuration
      expect(property.backwash_duration_minutes).toBeGreaterThan(0);
      expect(property.backwash_period_minutes).toBeGreaterThan(0);
      expect(property.backwash_delay_seconds).toBeGreaterThan(0);

      // Verify precipitation configuration
      expect(property.precipitation_volume_limit_mm).toBeGreaterThan(0);
      expect(property.precipitation_suspended_duration_hours).toBeGreaterThan(
        0
      );
      expect(property.rain_gauge_resolution_mm).toBeGreaterThan(0);
    });
  });

  it("should create water pumps with proper controller associations", async () => {
    await seedDatabase(trx);

    const waterPumps = await trx("water_pump").select("*");
    const irrigationPumps = waterPumps.filter(
      (wp: any) => wp.pump_type === "IRRIGATION"
    );
    const fertigationPumps = waterPumps.filter(
      (wp: any) => wp.pump_type === "FERTIGATION"
    );

    // All irrigation pumps should have controllers
    irrigationPumps.forEach((pump: any) => {
      expect(pump.water_pump_controller).toBeDefined();
    });

    // All fertigation pumps should have controllers
    fertigationPumps.forEach((pump: any) => {
      expect(pump.water_pump_controller).toBeDefined();
    });

    // Verify pump identifiers are properly formatted
    waterPumps.forEach((pump: any) => {
      expect(pump.identifier).toBeDefined();
      expect(pump.identifier.length).toBeGreaterThan(0);
      expect(pump.label).toBeDefined();
    });
  });
});
