/**
 * Random data generation utilities for seed operations
 */

const DAYS_OF_WEEK = ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"];

/**
 * Generate a random selection of days of the week
 */
export function randomDaysOfWeek(): string[] {
  const daysCount = Math.floor(Math.random() * DAYS_OF_WEEK.length) + 1;
  const selectedDays: string[] = [];
  const availableDays = [...DAYS_OF_WEEK];
  for (let i = 0; i < daysCount; i++) {
    const randomIndex = Math.floor(Math.random() * availableDays.length);
    selectedDays.push(availableDays[randomIndex]!);
    availableDays.splice(randomIndex, 1);
  }
  return selectedDays;
}
