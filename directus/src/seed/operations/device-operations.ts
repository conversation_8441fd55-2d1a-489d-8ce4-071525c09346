/**
 * Database operations for device-related entities
 */

import type { Knex } from "knex";
import type { DeviceData, PropertyDeviceData } from "../types";
import { daysAgo } from "../utils";

/**
 * Create a device in the database
 */
export async function createDevice(trx: Knex, data: DeviceData): Promise<string> {
  const [device] = await trx("device")
    .insert({
      model: data.model,
      identifier: data.identifier,
      notes: data.notes || null,
    })
    .returning("id");
  return device.id || device;
}

/**
 * Associate a device with a property
 */
export async function associateDeviceWithProperty(
  trx: Knex,
  data: PropertyDeviceData
): Promise<string> {
  const [pd] = await trx("property_device")
    .insert({
      device: data.device,
      property: data.property,
      // Backdate default start_date to ensure activity for historical mappings
      start_date: data.start_date || daysAgo(60),
    })
    .returning("id");
  return pd.id || pd;
}

/**
 * Create a mesh device mapping record
 */
export async function createMeshDeviceMapping(
  trx: Knex,
  data: {
    mesh_property_device: string;
    lic_property_device: string;
    start_date: Date;
    end_date?: Date | null;
  }
): Promise<string> {
  const [row] = await trx("mesh_device_mapping")
    .insert({
      mesh_property_device: data.mesh_property_device,
      lic_property_device: data.lic_property_device,
      start_date: data.start_date,
      end_date: data.end_date ?? null,
    })
    .returning("id");
  return row.id || row;
}
