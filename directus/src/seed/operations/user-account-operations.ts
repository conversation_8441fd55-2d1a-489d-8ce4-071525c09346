/**
 * Database operations for user and account-related entities
 */

import type { Knex } from "knex";
import { randomUUID } from "crypto";
import type { UserData, AccountUserData } from "../types";

const USER_ROLE_ID = "ac5ba5cb-1d74-4df4-99d3-6758fb49255c";

/**
 * Create a user in the database
 */
export async function createUser(trx: Knex, data: UserData): Promise<string> {
  const [user] = await trx("directus_users")
    .insert({
      id: randomUUID(),
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email,
      password: data.password,
      role: USER_ROLE_ID,
      status: "active",
      provider: "default",
    })
    .returning("id");
  return user.id || user;
}

/**
 * Create an account in the database
 */
export async function createAccount(
  trx: Knex,
  data: { ownerId: string }
): Promise<string> {
  const [account] = await trx("account")
    .insert({ owner: data.ownerId })
    .returning("id");
  return account.id || account;
}

/**
 * Create an account-user relationship
 */
export async function createAccountUser(
  trx: Knex,
  data: AccountUserData
): Promise<void> {
  await trx("account_user").insert({
    account: data.account,
    user: data.user,
    role: data.role || "admin",
    start_date: data.start_date || new Date(),
  });
}
