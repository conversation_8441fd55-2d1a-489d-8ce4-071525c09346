/**
 * Type definitions for property-related operations
 */

export interface PropertyData {
  account: string;
  name: string;
  timezone?: string;
  address_postal_code?: string;
  address_street_name?: string;
  address_street_number?: string;
  address_city?: string;
  address_state?: string;
  address_country?: string;
  backwash_duration_minutes?: number;
  backwash_period_minutes?: number;
  backwash_delay_seconds?: number;
  rain_gauge_enabled?: boolean;
  rain_gauge_resolution_mm?: number;
  precipitation_volume_limit_mm?: number;
  precipitation_suspended_duration_hours?: number;
}

export interface PropertiesData {
  joaquimAccountId: string;
  mariaAccountId: string;
  propertiesData: PropertyData[];
}
