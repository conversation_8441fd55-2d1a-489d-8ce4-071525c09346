/**
 * Type definitions for project-related operations
 */

import type { DeviceData } from "./device-types";
import type { WaterPumpData } from "./water-pump-types";

export interface ProjectData {
  property: string;
  name: string;
  description?: string;
  irrigation_water_pump: string;
  fertigation_water_pump?: string;
  localized_irrigation_controller: string;
  pipe_wash_time_seconds?: number;
  backwash_duration_seconds?: number;
  backwash_period_seconds?: number;
  start_date?: string;
  end_date?: string;
}

export interface SectorCreationData {
  project: string;
  property: string;
  projectName: string;
  propertyName: string;
  projectIndex: number;
  sectorsCount: number;
  vdDevicesData: DeviceData[];
  sectorsData: Array<{
    name: string;
    description: string;
    valveControllerIndex: number;
    valve_controller_output: number;
    area: number;
  }>;
}

export interface IrrigationPlanData {
  project: string;
  sectorIds: string[];
  plansData: Array<{
    name: string;
    description: string;
    start_time: string;
    days_of_week: string[];
    steps: Array<{
      description: string;
      order: number;
      duration_seconds: number;
      fertigation_start_delay_seconds?: number;
      fertigation_duration_seconds?: number;
    }>;
  }>;
}

export interface ProjectCreationData {
  property: string;
  propertyName: string;
  projectIndex: number;
  projectData: {
    name: string;
    description: string;
  };
  licDeviceData: DeviceData;
  irrigationWpcDeviceData: DeviceData;
  irrigationWaterPumpData: Omit<
    WaterPumpData,
    "property" | "water_pump_controller"
  >;
  fertigationWpcDeviceData?: DeviceData;
  fertigationWaterPumpData?: Omit<
    WaterPumpData,
    "property" | "water_pump_controller"
  >;
  sectorsData: SectorCreationData;
  irrigationPlansData: Omit<IrrigationPlanData, "project" | "sectorIds">;
}

export interface ProjectsForPropertyData {
  property: string;
  propertyName: string;
  projectsData: Array<ProjectCreationData>;
}
