/**
 * Type definitions for user and account-related operations
 */

export interface UserData {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
}

export interface AccountUserData {
  account: string;
  user: string;
  role?: "admin" | "user" | "guest";
  start_date?: Date;
}

export interface UsersAndAccountsData {
  passwordHash: string;
  usersData: UserData[];
}

export interface TestUserData {
  joaquimAccountId: string;
  mariaAccountId: string;
  userData: UserData;
}
