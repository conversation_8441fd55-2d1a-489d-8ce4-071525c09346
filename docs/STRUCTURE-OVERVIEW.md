# Irriga Mais System - Structure Overview

## System Architecture Analysis

The Irriga Mais project is a comprehensive agricultural irrigation management system built around a sophisticated multi-tenant cloud architecture. This document provides a structural analysis of the system's components, their relationships, and architectural patterns.

## System Evolution

### Legacy Component: IrrigaNet (`/irriganet/`)

**Type**: Android Native Application (Kotlin)  
**Purpose**: Direct LIC device configuration tool for field technicians  
**Status**: Deprecated - not used in the current system  
**Architecture**: Standalone mobile app with direct device communication

The IrrigaNet Android app represents the original approach to device management - a simple, field-focused tool that allowed technicians to configure LIC (Localized Irrigation Controller) devices directly. This legacy system demonstrates the evolution from local device management to comprehensive cloud-based infrastructure.

### Current System: Irriga Mais Cloud Platform

The modern Irriga Mais system represents a complete architectural transformation from local device management to a full-scale cloud-native platform supporting multi-tenancy, comprehensive device orchestration, and advanced irrigation management.

## Core Architecture Components

### 1. Frontend Layer (`/app/`)

**Technology Stack**: React 19 + TypeScript PWA  
**Runtime**: Bun  
**Architecture Pattern**: Modern SPA with PWA capabilities

**Key Architectural Decisions**:

- **State Management**: Jotai atomic state management for granular reactivity
- **Routing**: Wouter for lightweight client-side routing
- **Styling**: TailwindCSS v4 with custom design tokens
- **API Integration**: Directus SDK with typed interfaces
- **Progressive Web App**: Full PWA implementation with offline capabilities

**Structural Organization**:

```
src/
├── api/           # Directus SDK integration layer
├── components/    # Reusable UI components
├── pages/         # Route-based page components
├── store/         # Jotai atom definitions by domain
├── hooks/         # Custom React hooks
└── utils/         # Utility functions and helpers
```

The frontend demonstrates modern React patterns with a focus on type safety, performance optimization, and user experience across mobile and desktop platforms.

### 2. Backend Core (`/directus/`)

**Technology Stack**: Directus CMS + PostgreSQL + PostGIS  
**Architecture Pattern**: Headless CMS with spatial data capabilities

**System Responsibilities**:

- **Data Management**: Complete CRUD operations for all business entities
- **API Gateway**: RESTful API with GraphQL capabilities
- **Authentication**: JWT-based auth with role-based permissions
- **Spatial Data**: PostGIS integration for geographical features
- **Multi-tenancy**: Account-based data isolation

**Database Architecture**:

- **Hierarchical Structure**: Account → Property → Project → Sector
- **Device Management**: Comprehensive device lifecycle and association tracking
- **Constraint Enforcement**: Business rule validation at database level
- **Migration System**: Versioned schema evolution with Directus metadata

The backend leverages Directus as both a data management platform and API gateway, providing a robust foundation for the multi-tenant irrigation management system.

### 3. Device Communication Layer (`/mqtt-integration/`)

**Technology Stack**: TypeScript + Bun + MQTT  
**Architecture Pattern**: Event-driven microservice

**Primary Functions**:

- **Message Brokering**: Handles MQTT communication with field devices
- **Protocol Translation**: Converts Protocol Buffer messages to database operations
- **State Synchronization**: Maintains device state consistency between hardware and database
- **Device Orchestration**: Coordinates multi-device irrigation operations

This microservice represents the critical bridge between the physical irrigation hardware deployed in fields and the cloud-based management system.

### 4. Protocol Definition Layer (`/protobuf/`)

**Technology Stack**: Protocol Buffers + TypeScript code generation  
**Architecture Pattern**: Shared contract definition

**System Integration**:

- **Type Safety**: Generated TypeScript bindings for all device communications
- **Protocol Consistency**: Ensures message format consistency across all system components
- **Version Management**: Handles protocol evolution while maintaining backward compatibility

## System Integration Patterns

### Multi-Tenant Architecture

The system implements a sophisticated multi-tenant pattern:

1. **Account Level**: Top-level tenant isolation
2. **User-Account Relationships**: Many-to-many user access patterns
3. **Property-Based Access Control**: Granular permissions at property level
4. **Hierarchical Data Isolation**: Automatic data filtering based on tenant context

### Device Management Architecture

The system manages complex device relationships:

1. **Device Categories**: LIC, VC (Valve Controller), WPC (Water Pump Controller), RM (Reservoir Monitor)
2. **Mesh Network Topology**: LoRa mesh network with centralized coordination
3. **Property Association**: Devices linked to specific properties through mesh network mappings
4. **State Management**: Real-time device state synchronization via MQTT

### Data Flow Architecture

```
Field Devices (LoRa) → MQTT Broker → mqtt-integration → PostgreSQL ← Directus API ← React Frontend
```

This unidirectional data flow ensures consistency and enables real-time monitoring and control of irrigation systems.

## Operational Characteristics

### Development Workflow

- **Monorepo Structure**: Single repository containing all system components
- **Unified Runtime**: Bun used across frontend, backend testing, and integration services
- **Type Safety**: End-to-end TypeScript with generated Protocol Buffer bindings
- **Database-First**: Migration-driven development with comprehensive seed data

### Deployment Architecture

- **Containerization**: Docker-based deployment for all services
- **Service Orchestration**: Docker Compose for local development, production-ready containers
- **Database Management**: PostgreSQL with PostGIS for spatial data requirements
- **Static Asset Serving**: PWA with optimized asset delivery

### Testing Strategy

- **Backend Testing**: Transaction-based test isolation with comprehensive business logic validation
- **Seed Data Testing**: Production-like data scenarios for integration testing
- **Type Validation**: Compile-time type checking across all service boundaries

## Architectural Strengths

1. **Type Safety**: End-to-end TypeScript with Protocol Buffer integration
2. **Multi-tenancy**: Robust account-based isolation with granular permissions
3. **Real-time Communication**: MQTT-based device communication with state synchronization
4. **Spatial Capabilities**: PostGIS integration for geographical irrigation management
5. **Progressive Enhancement**: PWA capabilities for field use in low-connectivity environments
6. **Modular Design**: Clear separation of concerns across service boundaries

## System Scale and Complexity

The Irriga Mais system represents a significant advancement in agricultural technology architecture:

- **Multi-tenant SaaS Platform**: Supporting multiple agricultural operations simultaneously
- **IoT Device Management**: Comprehensive lifecycle management for field devices
- **Real-time Control Systems**: Coordinated irrigation scheduling and execution
- **Spatial Data Management**: Geographic information system capabilities for precision agriculture
- **Mobile-First Design**: Optimized for field technicians and farm managers

This architecture demonstrates a modern approach to agricultural technology, combining cloud-native development practices with domain-specific requirements for irrigation management and IoT device coordination.

## Database Architecture

### Schema Design Philosophy

The database architecture follows a sophisticated hierarchical model designed to support multi-tenant irrigation management with complex device relationships and temporal data tracking. The schema is built on PostgreSQL with PostGIS extensions, providing both relational integrity and spatial data capabilities.

### Core Entity Architecture

#### Hierarchical Business Structure

```
Account (Tenant) → Property (Farm) → Project (Irrigation System) → Sector (Irrigation Zone)
```

**Account Table**: Top-level tenant isolation with unique owner relationships

- Primary multi-tenancy mechanism
- One-to-one with account owner (directus_users)
- Metadata support for extensible properties

**Property Table**: Geographical properties with comprehensive addressing and spatial data

- PostGIS Point geometry for location tracking
- Rain gauge configuration for weather-based irrigation control
- Timezone support for proper scheduling across regions
- Address normalization with structured fields

**Project Table**: Irrigation project management within properties

- References to irrigation and fertigation water pumps
- Localized Irrigation Controller (LIC) association
- Temporal project lifecycles with start/end dates
- Exclusion constraints preventing overlapping projects

**Sector Table**: Individual irrigation zones with valve control

- Valve controller assignment with specific output mapping (1-4 outputs per controller)
- PostGIS Polygon geometry for area definition
- Power consumption tracking (0-100% scale)
- Area measurement support for irrigation calculations

#### Device Management Schema

**Device Table**: Hardware device registry with model-based constraints

- Unique identifier system for field device tracking
- Model enumeration: LIC, WPC-PL10, WPC-PL50, VC, RM
- Metadata JSONB field for extensible device properties

**Property_Device Table**: Temporal device assignments with sophisticated overlap prevention

- Time-based device associations with properties
- Exclusion constraints using PostgreSQL GiST indexes
- Current mesh mapping tracking for network topology
- Support for device reassignment without data loss

### Advanced Relationship Modeling

#### Mesh Network Architecture

**Mesh_Device_Mapping Table**: Complex device network associations

- Many-to-many relationships between mesh devices and LIC controllers
- Temporal validity with accommodation logic for overlapping periods
- Sophisticated trigger system for maintaining network integrity
- Automatic resolution of mapping conflicts through period adjustment

**Mesh Constraint Functions**: Database-enforced business rules

- `get_lic_for_device()`: Resolves LIC associations for any mesh device
- `check_reservoir_mesh()`: Ensures reservoir components share same mesh network
- `check_project_mesh()`: Validates project device mesh consistency
- `check_sector_mesh()`: Enforces sector-project mesh alignment

#### Water Management Schema

**Water_Pump Table**: Comprehensive pump management

- Type classification: IRRIGATION, FERTIGATION, SERVICE
- Controller device associations through foreign keys
- Operation mode support: PULSE, CONTINUOUS
- Flow rate tracking for irrigation calculations
- Frequency inverter and monitoring capability flags

**Reservoir Table**: Water storage and monitoring system

- Capacity tracking with numeric precision
- Safety time configuration for critical operation management
- PostGIS Point geometry for location tracking
- Unique associations with reservoir monitors and service pumps

### Irrigation Planning Schema

#### Schedule Management

**Irrigation_Plan Table**: Comprehensive irrigation scheduling

- Time-based execution with days-of-week JSON arrays
- Fertigation and backwash capability flags
- Automatic total duration calculation through triggers
- Project-scoped plan management with unique naming

**Irrigation_Plan_Step Table**: Detailed execution sequences

- Ordered step execution with deferrable unique constraints
- Sector-specific irrigation durations
- Fertigation timing control with delay and duration parameters
- Automatic plan duration recalculation on modifications

### Data Integrity and Performance

#### Temporal Data Management

- **Timestamp Triggers**: Automatic `date_updated` maintenance across all tables
- **Temporal Constraints**: Range-based exclusion constraints for preventing overlaps
- **Validity Periods**: Start/end date patterns for temporal relationships
- **Cascade Controls**: Careful DELETE/UPDATE cascade management

#### Spatial Data Integration

- **PostGIS Integration**: Point and Polygon geometry support
- **SRID 4326**: WGS84 coordinate system for global compatibility
- **Spatial Indexing**: Automatic spatial indexes for geometry queries
- **Location Tracking**: Property and reservoir location management

#### Business Rule Enforcement

- **Check Constraints**: Enumerated value validation and range checking
- **Trigger Functions**: Complex business logic enforcement at database level
- **Referential Integrity**: Strict foreign key relationships with controlled cascading
- **Unique Constraints**: Composite uniqueness for hierarchical naming

#### Performance Optimization

- **Strategic Indexing**: B-tree indexes on foreign keys and frequent query columns
- **GiST Indexes**: Specialized indexes for temporal and spatial data
- **JSONB Fields**: Efficient storage and querying of metadata
- **Query Optimization**: Index support for common access patterns

### Multi-tenancy Implementation

#### Account-Based Isolation

- **Row-Level Security**: Account-based data filtering (implemented through application layer)
- **Hierarchical Access**: Property-level permission inheritance
- **User-Account Relationships**: Many-to-many user access with role management
- **Temporal User Access**: Time-bounded user-account associations

#### Data Segregation Patterns

- **Account Scoping**: All business data ultimately scoped to account entities
- **Property Isolation**: Device and project data isolated within properties
- **Cross-Account Prevention**: Foreign key constraints prevent cross-account data leakage
- **Audit Trails**: User tracking for all modifications with Directus integration

This database architecture demonstrates enterprise-grade data modeling for complex IoT systems, providing the foundation for reliable, scalable irrigation management while maintaining data integrity and supporting sophisticated business logic enforcement at the database level.
