import { describe, it, expect } from "bun:test";
import { listReservoirsWithMonitorAndWaterPumpByLICIdentifier } from "../src/db/queries/reservoir";
import { runInTransaction } from "./helpers/db";
import {
  insertAccount,
  insertProperty,
  insertDevice,
  insertPropertyDevice,
  insertUser,
  insertWaterPump,
  insertMeshMapping,
  insertReservoir,
} from "./helpers/fixtures";

describe("reservoir-queries", () => {
  it("should return reservoirs with monitor and water pump controller for LIC", async () => {
    await runInTransaction(async (trx) => {
      const user = await insertUser(trx);
      const account = await insertAccount(trx, user.id);
      const property = await insertProperty(trx, account.id, "Test Property");

      // LIC device and its property_device
      const licDevice = await insertDevice(trx, "LIC", "LIC-RES-001");
      const startDate = new Date("2024-01-01");
      const endDate = new Date("2024-12-31");
      const licPd = await insertPropertyDevice(
        trx,
        licDevice.id,
        property.id,
        startDate,
        endDate
      );

      // Mesh monitor device and its property_device + mapping
      const meshMonitor = await insertDevice(trx, "RM", "MESH-MON-001");
      const meshPdMonitor = await insertPropertyDevice(
        trx,
        meshMonitor.id,
        property.id,
        startDate,
        endDate
      );
      await insertMeshMapping(
        trx,
        meshPdMonitor.id,
        licPd.id,
        startDate,
        endDate
      );

      // Water pump controller, water pump and its controller mapping
      const meshWpController = await insertDevice(
        trx,
        "WPC-PL10",
        "WPC-CTRL-001"
      );
      // create property_device for controller and mapping
      const meshPdWp = await insertPropertyDevice(
        trx,
        meshWpController.id,
        property.id,
        startDate,
        endDate
      );
      // create water pump referencing the controller
      const waterPump = await insertWaterPump(
        trx,
        property.id,
        "SERVICE",
        "WP-ONE",
        "WP-001",
        meshWpController.id
      );
      await insertMeshMapping(trx, meshPdWp.id, licPd.id, startDate, endDate);

      // Create reservoir pointing to mesh monitor and water pump
      const reservoir = await insertReservoir(
        trx,
        property.id,
        meshMonitor.id,
        waterPump.id,
        "Main Reservoir",
        123.45,
        null
      );

      const results =
        await listReservoirsWithMonitorAndWaterPumpByLICIdentifier(
          trx,
          "LIC-RES-001",
          new Date("2024-06-01")
        );

      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBeGreaterThan(0);

      expect(results[0]?.id).toBe(reservoir.id);
      // monitor should be present and match identifier
      expect(results[0]?.reservoir_monitor).toBeDefined();
      expect(results[0]?.reservoir_monitor?.identifier).toBe("MESH-MON-001");
      // water_pump should be present and include controller
      expect(results[0]?.water_pump).toBeDefined();
      expect(results[0]?.water_pump?.identifier).toBe("WP-001");
      expect(results[0]?.water_pump?.water_pump_controller).toBeDefined();
      expect(results[0]?.water_pump?.water_pump_controller?.identifier).toBe(
        "WPC-CTRL-001"
      );
    });
  });

  it("should return empty array when LIC has no reservoirs mapped", async () => {
    await runInTransaction(async (trx) => {
      const results =
        await listReservoirsWithMonitorAndWaterPumpByLICIdentifier(
          trx,
          "NONEXISTENT-LIC",
          new Date()
        );
      expect(results).toEqual([]);
    });
  });
});
