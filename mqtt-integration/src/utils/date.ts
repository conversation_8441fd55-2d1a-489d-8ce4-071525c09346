export function date(date: null): null;
export function date(date: undefined): undefined;
export function date(date: string | Date | number): Date;
export function date(
  date: string | Date | number | null | undefined
): Date | null | undefined;
export function date(
  date: string | Date | number | null | undefined
): Date | null | undefined {
  if (date === null) {
    return null;
  }
  if (date === undefined) {
    return undefined;
  }
  if (typeof date === "string") {
    return new Date(date);
  }
  if (typeof date === "number") {
    return new Date(date);
  }
  return date;
}
