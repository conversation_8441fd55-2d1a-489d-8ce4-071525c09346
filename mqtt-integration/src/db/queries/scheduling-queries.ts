import type { S<PERSON> } from "bun";
import type {
  IrrigationPlanWithStepsAndSectors,
  ProjectWithSchedulingData,
} from "./types";

/**
 * Returns enabled irrigation plans for projects managed by the specified LIC identifier.
 * Includes comprehensive data for building scheduling packages.
 *
 * @param db Database connection
 * @param licIdentifier LIC device identifier
 * @param referenceDate Reference date for active records
 * @returns List of irrigation plans with steps and sector information
 */
export async function listIrrigationPlansByLICIdentifier(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<IrrigationPlanWithStepsAndSectors[]> {
  return await db<IrrigationPlanWithStepsAndSectors[]>`
    SELECT 
      ip.*,
      (
        SELECT COALESCE(
          jsonb_agg(
            jsonb_build_object(
              'id', ips.id,
              'irrigation_plan', ips.irrigation_plan,
              'sector', ips.sector,
              'description', ips.description,
              'order', ips."order",
              'duration_seconds', ips.duration_seconds,
              'fertigation_start_delay_seconds', ips.fertigation_start_delay_seconds,
              'fertigation_duration_seconds', ips.fertigation_duration_seconds,
              'date_created', ips.date_created,
              'user_created', ips.user_created,
              'date_updated', ips.date_updated,
              'user_updated', ips.user_updated,
              'metadata', ips.metadata,
              'notes', ips.notes,
              'sector_info', jsonb_build_object(
                'id', s.id,
                'project', s.project,
                'name', s.name,
                'valve_controller', s.valve_controller,
                'valve_controller_output', s.valve_controller_output,
                'power', s.power,
                'description', s.description,
                'date_created', s.date_created,
                'user_created', s.user_created,
                'date_updated', s.date_updated,
                'user_updated', s.user_updated,
                'metadata', s.metadata,
                'notes', s.notes,
                'valve_controller_device', jsonb_build_object(
                  'id', vcd.id,
                  'identifier', vcd.identifier,
                  'model', vcd.model,
                  'date_created', vcd.date_created,
                  'user_created', vcd.user_created,
                  'date_updated', vcd.date_updated,
                  'user_updated', vcd.user_updated,
                  'metadata', vcd.metadata,
                  'notes', vcd.notes
                )
              )
            ) ORDER BY ips."order" ASC
          ),
          '[]'::jsonb
        )
        FROM irrigation_plan_step ips
        JOIN sector s ON ips.sector = s.id
        LEFT JOIN device vcd ON s.valve_controller = vcd.id
        WHERE ips.irrigation_plan = ip.id
      ) AS steps
    FROM irrigation_plan ip
    JOIN project p ON ip.project = p.id
    JOIN property_device pd ON p.property = pd.property
    JOIN device d ON pd.device = d.id
    WHERE d.identifier = ${licIdentifier}
      AND d.model = 'LIC'
      AND ${referenceDate} >= COALESCE(pd.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(pd.end_date, 'infinity'::timestamp)
      AND p.localized_irrigation_controller = d.id
      AND ${referenceDate} >= COALESCE(p.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(p.end_date, 'infinity'::timestamp)
      AND ip.is_enabled = true
      AND (ip.start_date IS NULL OR ${referenceDate} >= ip.start_date)
      AND (ip.end_date IS NULL OR ${referenceDate} <= ip.end_date)
    ORDER BY p.date_created ASC, ip.date_created ASC
  `;
}

/**
 * Returns complete project data with irrigation plans for the specified LIC identifier.
 * This provides all necessary data for building SchedulingPackage messages.
 *
 * @param db Database connection
 * @param licIdentifier LIC device identifier
 * @param referenceDate Reference date for active records
 * @returns List of projects with complete scheduling data
 */
export async function listProjectsWithSchedulingByLICIdentifier(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<ProjectWithSchedulingData[]> {
  return await db<ProjectWithSchedulingData[]>`
    SELECT
      p.*,
      
      -- Localized Irrigation Controller (LIC) as a single JSON object
      ( SELECT
          jsonb_build_object(
            'id', d.id,
            'identifier', d.identifier,
            'model', d.model,
            'date_created', d.date_created,
            'user_created', d.user_created,
            'date_updated', d.date_updated,
            'user_updated', d.user_updated,
            'metadata', d.metadata,
            'notes', d.notes
          )
        FROM property_device pd
        JOIN device d ON pd.device = d.id
        WHERE pd.property = p.property
          AND d.id = p.localized_irrigation_controller
          AND d.identifier = ${licIdentifier}
          AND d.model = 'LIC'
          AND ${referenceDate} >= COALESCE(pd.start_date, '-infinity'::timestamp)
          AND ${referenceDate} <= COALESCE(pd.end_date, 'infinity'::timestamp)
        LIMIT 1
      ) AS localized_irrigation_controller,

      -- Irrigation Water Pump with controller
      ( SELECT
          CASE WHEN iwp.id IS NOT NULL THEN
            jsonb_build_object(
              'id', iwp.id,
              'property', iwp.property,
              'label', iwp.label,
              'identifier', iwp.identifier,
              'pump_type', iwp.pump_type,
              'pump_model', iwp.pump_model,
              'has_frequency_inverter', iwp.has_frequency_inverter,
              'monitor_operation', iwp.monitor_operation,
              'flow_rate_lh', iwp.flow_rate_lh,
              'mode', iwp.mode,
              'notes', iwp.notes,
              'metadata', iwp.metadata,
              'date_created', iwp.date_created,
              'user_created', iwp.user_created,
              'date_updated', iwp.date_updated,
              'user_updated', iwp.user_updated,
              'water_pump_controller', jsonb_build_object(
                'id', ctrl.id,
                'identifier', ctrl.identifier,
                'model', ctrl.model,
                'date_created', ctrl.date_created,
                'user_created', ctrl.user_created,
                'date_updated', ctrl.date_updated,
                'user_updated', ctrl.user_updated,
                'metadata', ctrl.metadata,
                'notes', ctrl.notes
              )
            )
          ELSE NULL END
        FROM water_pump iwp
        LEFT JOIN device ctrl ON iwp.water_pump_controller = ctrl.id
          AND ctrl.model IN ('WPC-PL10', 'WPC-PL50')
        WHERE iwp.id = p.irrigation_water_pump
        LIMIT 1
      ) AS irrigation_water_pump,

      -- Fertigation Water Pump with controller
      ( SELECT
          CASE WHEN fwp.id IS NOT NULL THEN
            jsonb_build_object(
              'id', fwp.id,
              'property', fwp.property,
              'label', fwp.label,
              'identifier', fwp.identifier,
              'pump_type', fwp.pump_type,
              'pump_model', fwp.pump_model,
              'has_frequency_inverter', fwp.has_frequency_inverter,
              'monitor_operation', fwp.monitor_operation,
              'flow_rate_lh', fwp.flow_rate_lh,
              'mode', fwp.mode,
              'notes', fwp.notes,
              'metadata', fwp.metadata,
              'date_created', fwp.date_created,
              'user_created', fwp.user_created,
              'date_updated', fwp.date_updated,
              'user_updated', fwp.user_updated,
              'water_pump_controller', jsonb_build_object(
                'id', ctrl2.id,
                'identifier', ctrl2.identifier,
                'model', ctrl2.model,
                'date_created', ctrl2.date_created,
                'user_created', ctrl2.user_created,
                'date_updated', ctrl2.date_updated,
                'user_updated', ctrl2.user_updated,
                'metadata', ctrl2.metadata,
                'notes', ctrl2.notes
              )
            )
          ELSE NULL END
        FROM water_pump fwp
        LEFT JOIN device ctrl2 ON fwp.water_pump_controller = ctrl2.id
          AND ctrl2.model IN ('WPC-PL10', 'WPC-PL50')
        WHERE fwp.id = p.fertigation_water_pump
        LIMIT 1
      ) AS fertigation_water_pump,

      -- Sectors array with valve controller devices
(
  SELECT COALESCE(
    jsonb_agg(sector_obj ORDER BY (sector_obj->>'name') ASC),  -- final array sort
    '[]'::jsonb
  )
  FROM (
    SELECT DISTINCT ON (s.id)
      jsonb_build_object(
        'id', s.id,
        'project', s.project,
        'name', s.name,
        'valve_controller', s.valve_controller,
        'valve_controller_output', s.valve_controller_output,
        'power', s.power,
        'description', s.description,
        'date_created', s.date_created,
        'user_created', s.user_created,
        'date_updated', s.date_updated,
        'user_updated', s.user_updated,
        'metadata', s.metadata,
        'notes', s.notes,
        'valve_controller_device', jsonb_build_object(
          'id', vc.id,
          'identifier', vc.identifier,
          'model', vc.model,
          'date_created', vc.date_created,
          'user_created', vc.user_created,
          'date_updated', vc.date_updated,
          'user_updated', vc.user_updated,
          'metadata', vc.metadata,
          'notes', vc.notes
        )
      ) AS sector_obj
    FROM sector s
    LEFT JOIN property_device vc_pd ON s.valve_controller = vc_pd.device
      AND vc_pd.property = p.property
      AND ${referenceDate} >= COALESCE(vc_pd.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(vc_pd.end_date, 'infinity'::timestamp)
    LEFT JOIN device vc ON vc_pd.device = vc.id
      AND vc.model = 'VC'
    WHERE s.project = p.id
    ORDER BY s.id, s.name  -- pick one row per sector (dedupe), stable by name
  ) t
) AS sectors,

      -- Irrigation Plans (sorted by COALESCE(ip.start_date, ip.date_created); no sector_info)
(
  SELECT COALESCE(
    jsonb_agg(plan_obj ORDER BY sort_key ASC),
    '[]'::jsonb
  )
  FROM (
    SELECT DISTINCT ON (ip.id)
      COALESCE(ip.start_date, ip.date_created) AS sort_key,
      jsonb_build_object(
        'id', ip.id,
        'project', ip.project,
        'name', ip.name,
        'description', ip.description,
        'start_time', ip.start_time,
        'days_of_week', ip.days_of_week,
        'is_enabled', ip.is_enabled,
        'fertigation_enabled', ip.fertigation_enabled,
        'total_irrigation_duration', ip.total_irrigation_duration,
        'start_date', ip.start_date,
        'end_date', ip.end_date,
        'date_created', ip.date_created,
        'user_created', ip.user_created,
        'date_updated', ip.date_updated,
        'user_updated', ip.user_updated,
        'metadata', ip.metadata,
        'notes', ip.notes,
        'steps', COALESCE(
          (
            SELECT jsonb_agg(
                     jsonb_build_object(
                       'id', ips.id,
                       'irrigation_plan', ips.irrigation_plan,
                       'sector', ips.sector,
                       'description', ips.description,
                       'order', ips."order",
                       'duration_seconds', ips.duration_seconds,
                       'fertigation_start_delay_seconds', ips.fertigation_start_delay_seconds,
                       'fertigation_duration_seconds', ips.fertigation_duration_seconds,
                       'date_created', ips.date_created,
                       'user_created', ips.user_created,
                       'date_updated', ips.date_updated,
                       'user_updated', ips.user_updated,
                       'metadata', ips.metadata,
                       'notes', ips.notes
                     )
                     ORDER BY ips."order" ASC
                   )
            FROM irrigation_plan_step ips
            WHERE ips.irrigation_plan = ip.id
          ),
          '[]'::jsonb
        )
      ) AS plan_obj
    FROM irrigation_plan ip
    WHERE ip.project = p.id
      AND ip.is_enabled = true
      AND (${referenceDate.toISOString()})::date >= COALESCE(ip.start_date, '-infinity'::date)
      AND (${referenceDate.toISOString()})::date <= COALESCE(ip.end_date, 'infinity'::date)
    -- DISTINCT ON picker + stable secondary by sort key
    ORDER BY ip.id, COALESCE(ip.start_date, ip.date_created)
  ) t
) AS irrigation_plans


    FROM project p
    WHERE EXISTS (
      SELECT 1
      FROM property_device pd2
      JOIN device d2 ON pd2.device = d2.id
      WHERE pd2.property = p.property
        AND d2.identifier = ${licIdentifier}
        AND d2.model = 'LIC'
        AND ${referenceDate} >= COALESCE(pd2.start_date, '-infinity'::timestamp)
        AND ${referenceDate} <= COALESCE(pd2.end_date, 'infinity'::timestamp)
        AND p.localized_irrigation_controller = d2.id
    )
      AND ${referenceDate} >= COALESCE(p.start_date, '-infinity'::timestamp)
      AND ${referenceDate} <= COALESCE(p.end_date, 'infinity'::timestamp)
    ORDER BY p.date_created ASC
  `;
}
