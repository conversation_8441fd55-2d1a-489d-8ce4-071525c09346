/**
 * IrrigaNet Database Types
 *
 * This file contains TypeScript interfaces and type definitions that correspond
 * to the SQLite database schema used in the IrrigaNet irrigation management system.
 * These types are designed for use with MQTT integration and data synchronization
 * between the Android application and external systems.
 */

import type { codec } from "proto";

// ==============================================
// CORE DATABASE TYPES
// ==============================================

/**
 * Base interface for all database entities with an auto-incrementing primary key
 */
interface DatabaseEntity {
  idx: number;
}

/**
 * Base interface for entities that have an order index for sequencing
 */
interface OrderIndexedEntity extends DatabaseEntity {
  ord_idx: number;
}

// ==============================================
// CODEC TABLE TYPES
// ==============================================

/**
 * Represents a codec (communication hub) that controls irrigation systems.
 * Codecs are the main communication hubs that manage multiple groups of devices
 * and maintain WiFi connectivity for the irrigation network.
 */
export interface IrriganetCodec extends DatabaseEntity {
  identity: string; // Unique identifier for the codec device
  name: string; // Human-readable name for the codec
  wifi_ssid?: string; // WiFi network name for codec connectivity
  wifi_passwd?: string; // WiFi password for network access
  last_devices_update?: number; // Timestamp of last devices synchronization
  last_scheduling_update?: number; // Timestamp of last scheduling synchronization
  last_device_scheduling_update?: number; // Timestamp of last device scheduling sync
  last_automation_update?: number; // Timestamp of last automation sync
  last_config_update?: number; // Timestamp of last configuration sync
  enabled: number; // Flag to enable/disable the codec (1=enabled, 0=disabled)
}

// ==============================================
// GROUPS TABLE TYPES
// ==============================================

/**
 * Represents a logical group of mesh devices under a single codec.
 * Groups provide an organizational layer between codecs and devices,
 * allowing devices to be organized by location, function, or management needs.
 */
export interface IrriganetGroup extends DatabaseEntity {
  name: string; // Human-readable name for the group
  codec_idx?: number; // Foreign key reference to the parent codec
}

// ==============================================
// MESH DEVICES TABLE TYPES
// ==============================================

/**
 * Represents a physical mesh network device that can contain multiple individual devices.
 * Mesh devices are hardware units deployed in the field that act as intermediaries
 * between the codec and individual control devices.
 */
export interface IrriganetMeshDevice extends DatabaseEntity {
  identity: string; // Unique identifier for the mesh device
  name: string; // Human-readable name for the mesh device
  type?: number; // Device type classification (0=standard, other values for specialized types)
  mode?: number; // Operating mode of the device
  equipament?: number; // Equipment type identifier
  check_input?: number; // Flag for input monitoring capability
  devices_bitmask?: number; // Bitmask representing connected sub-devices
  level_pump_idx?: number; // Database ID of device to use as level pump
  level_pump_enable?: number; // Whether level pump functionality is enabled
  level_pump_working_time?: number; // Working time for level pump operations
  codec_idx?: number; // Foreign key to parent codec
  group_idx?: number; // Foreign key to parent group
}

// ==============================================
// DEVICES TABLE TYPES
// ==============================================

/**
 * Represents an individual controllable component like valves, pumps, or sensors.
 * Multiple devices can exist within a single mesh device and represent the
 * actual irrigation control elements.
 */
export interface IrriganetDevice extends OrderIndexedEntity {
  mesh_idx: number; // Foreign key to parent mesh device
  identity: string; // Unique identifier within the mesh device
  type: number; // Device type (0=sector/valve, 1=pump, 2=fertilizer, etc.)
  out1?: number; // Output channel 1 configuration
  out2?: number; // Output channel 2 configuration
  input?: number; // Input channel configuration
  mode?: number; // Operating mode
  sector?: number; // Sector number for irrigation zones
  power?: number; // Power configuration
  eqpt_ver?: number; // Equipment version identifier
}

// ==============================================
// SCHEDULINGS TABLE TYPES
// ==============================================

/**
 * Represents an irrigation scheduling configuration.
 * Schedulings define when and how irrigation should occur, including timing,
 * fertilization, and backwash configurations for specific groups.
 */
export interface IrriganetScheduling extends OrderIndexedEntity {
  group_idx: number; // Foreign key to parent group
  name: string; // Human-readable name for the schedule
  hour: number; // Start hour (0-23, 24-hour format)
  min: number; // Start minute (0-59)
  start_time: number; // Start time in minutes from midnight
  end_time?: number; // Optional end time constraint in minutes from midnight
  days_of_week: number; // Bitmask for days (bit 0=Sunday, bit 1=Monday, etc.)
  number_of_steps: number; // Number of irrigation steps/phases
  allow_ferti: number; // Whether fertilizer injection is allowed (1=allowed, 0=not allowed)
  allow_backwash: number; // Whether backwash operation is allowed (1=allowed, 0=not allowed)
  waterpump_idx?: number; // Database ID of water pump device
  waterpump_ord_idx?: number; // Order index of water pump device
  waterpump_working_time?: number; // Working time for water pump operations
  ferti_idx?: number; // Database ID of fertilizer device
  ferti_ord_idx?: number; // Order index of fertilizer device
  backwash_idx?: number; // Database ID of backwash device
  backwash_ord_idx?: number; // Order index of backwash device
  once?: number; // Whether this is a one-time schedule (1=once, 0=recurring)
  enabled: number; // Whether this schedule is active (1=active, 0=inactive)
}

// ==============================================
// SECTOR SCHEDULINGS TABLE TYPES
// ==============================================

/**
 * Represents sector-specific scheduling details within a scheduling.
 * Sector schedulings define which sectors (irrigation zones) are activated
 * during specific scheduling periods and their operational parameters.
 */
export interface IrriganetSectorScheduling extends DatabaseEntity {
  scheduling_idx: number; // Foreign key to parent scheduling
  device_idx: number; // Foreign key to the controlled device
  n_order: number; // Execution order within the schedule
  enabled: number; // Whether this sector scheduling is active (1=active, 0=inactive)
  type: number; // Type of operation
  ferti: number; // Fertilizer injection flag
  ferti_delay: number; // Delay before fertilizer injection (seconds)
  working_time: number; // Duration of operation (seconds)
}

// ==============================================
// DEVICE SCHEDULINGS TABLE TYPES
// ==============================================

/**
 * Represents detailed scheduling assignments for individual devices.
 * Device schedulings store the operational sequence and parameters for
 * individual devices during scheduled irrigation periods.
 */
export interface IrriganetDeviceScheduling extends OrderIndexedEntity {
  scheduling_idx: number; // Foreign key to parent scheduling
  device_idx: number; // Foreign key to the controlled device
  n_order: number; // Execution order within the schedule
  status: number; // Current operational status
  type: number; // Type of device operation
  time: number; // Scheduled execution time
  sector_working_time: number; // Duration for sector operation (seconds)
  ferti_working_time?: number; // Duration for fertilizer operation (seconds)
  ferti_delay: number; // Delay before fertilizer injection (seconds)
}

// ==============================================
// CODEC CONFIGURATION
// ==============================================

/**
 * Although it is not a SQLite table, it is a important structure.
 * In the irriganet Android app it is mostly stored in SharedPreferences.
 */
export interface IrriganetCodecConfig extends codec.in_.config.IConfigPackage {}

// ==============================================
// DATA TRANSFER AND SYNCHRONIZATION TYPES
// ==============================================

/**
 * Simplified codec item for basic codec information
 */
export interface CodecItem {
  idx: number;
  identity: string;
  name: string;
}

/**
 * Simplified group item for basic group information
 */
export interface GroupItem {
  idx: number;
  name: string;
}

/**
 * Simplified mesh device item for basic mesh device information
 */
export interface MeshDeviceItem {
  idx: number;
  identity: string;
  name: string;
  label: string;
}

/**
 * Simplified device item for basic device information
 */
export interface DeviceItem {
  idx: number;
  ord_idx: number;
  name: string;
  type: number;
  device_identity: string;
  mesh_identity: string;
  codec_identity?: string;
  sector?: string;
  power?: number;
  eqpt_ver?: number;
}

/**
 * Simplified sector scheduling item for basic sector scheduling information
 */
export interface SectorSchedulingItem {
  device_idx: number;
  type: number;
  n_order: number;
  enabled: boolean;
  sector: string;
  working_time: string;
  ferti: string;
  ferti_delay: string;
}

// ==============================================
// QUERY AND RESPONSE TYPES
// ==============================================

/**
 * Response type for getting codecs with complete information
 */
export interface CodecMap {
  idx: number;
  identity: string;
  name: string;
  wifi_ssid?: string;
  wifi_passwd?: string;
  last_devices_update?: number;
  last_scheduling_update?: number;
  last_device_scheduling_update?: number;
  last_automation_update?: number;
  last_config_update?: number;
}

/**
 * Response type for getting schedulings with complete information
 */
export interface SchedulingMap {
  idx: number;
  ord_idx: number;
  group_idx: number;
  name: string;
  hour: number;
  min: number;
  start_time: number;
  end_time?: number;
  days_of_week: number;
  number_of_steps: number;
  allow_ferti: number;
  allow_backwash: number;
  enabled: number;
  waterpump_idx?: number;
  waterpump_ord_idx?: number;
  waterpump_working_time?: number;
  ferti_idx?: number;
  ferti_ord_idx?: number;
  backwash_idx?: number;
  backwash_ord_idx?: number;
  once?: number;
  codec_identity?: string;
}

/**
 * Response type for getting devices with complete information
 */
export interface DeviceMap {
  idx: number;
  mesh_idx: number;
  identity: string;
  type: number;
  out1?: number;
  out2?: number;
  input?: number;
  mode?: number;
  sector?: number;
  power?: number;
  eqpt_ver?: number;
  mesh_name?: string;
}

/**
 * Response type for compact device scheduling data (used for MQTT)
 */
export interface CompactDeviceScheduling {
  ix: number; // ord_idx
  sh: number; // scheduling_ord_idx
  dx: number; // device_ord_idx
  od: number; // n_order
  status: number; // status
  tp: number; // type
  tm: number; // time
  sector_time?: number; // sector_working_time
  ft: number; // ferti_working_time
  fd?: number; // ferti_delay
  dw?: number; // days_of_week
}

/**
 * Device specification for synchronization operations
 */
export interface DeviceSpecification {
  identity?: string | number;
  type?: number;
  out1?: string | number;
  out2?: string | number;
  input?: string | number;
  mode?: number;
  sector?: string | number;
  ord_idx?: number;
}

/**
 * Level pump device configuration
 */
export interface LevelPumpDevice {
  li: number; // device_ord_idx
  pi: number; // level_pump_ord_idx
  wt: number; // level_pump_working_time
}

// ==============================================
// ENUMS AND CONSTANTS
// ==============================================

/**
 * Device type enumeration
 */
export enum DeviceType {
  Sector = 0, // Sector/valve
  Pump = 1, // Water pump
  Fertilizer = 2, // Fertilizer injector
  Backwash = 3, // Backwash system
  Level = 4, // Level control
}

/**
 * Mesh device type enumeration
 */
export enum MeshDeviceType {
  Standard = 0, // Standard mesh device
  Pump = 1, // Pump device
  Level = 2, // Level control device
  // Add other types as defined in the system
}

/**
 * Days of week bitmask constants
 */
export const DaysOfWeek = {
  Sunday: 1, // Bit 0
  Monday: 2, // Bit 1
  Tuesday: 4, // Bit 2
  Wednesday: 8, // Bit 3
  Thursday: 16, // Bit 4
  Friday: 32, // Bit 5
  Saturday: 64, // Bit 6
} as const;

/**
 * Helper function to create days of week bitmask
 */
export function createDaysOfWeekBitmask(days: string[]): number {
  let bitmask = 0;
  const dayMap: Record<string, number> = {
    Sunday: DaysOfWeek.Sunday,
    Monday: DaysOfWeek.Monday,
    Tuesday: DaysOfWeek.Tuesday,
    Wednesday: DaysOfWeek.Wednesday,
    Thursday: DaysOfWeek.Thursday,
    Friday: DaysOfWeek.Friday,
    Saturday: DaysOfWeek.Saturday,
    Dom: DaysOfWeek.Sunday,
    Seg: DaysOfWeek.Monday,
    Ter: DaysOfWeek.Tuesday,
    Qua: DaysOfWeek.Wednesday,
    Qui: DaysOfWeek.Thursday,
    Sex: DaysOfWeek.Friday,
    Sáb: DaysOfWeek.Saturday,
  };

  for (const day of days) {
    if (dayMap[day]) {
      bitmask |= dayMap[day];
    }
  }

  return bitmask;
}

/**
 * Helper function to check if a day is included in the bitmask
 */
export function isDayIncluded(bitmask: number, day: string): boolean {
  const dayMap: Record<string, number> = {
    Sunday: DaysOfWeek.Sunday,
    Monday: DaysOfWeek.Monday,
    Tuesday: DaysOfWeek.Tuesday,
    Wednesday: DaysOfWeek.Wednesday,
    Thursday: DaysOfWeek.Thursday,
    Friday: DaysOfWeek.Friday,
    Saturday: DaysOfWeek.Saturday,
    Dom: DaysOfWeek.Sunday,
    Seg: DaysOfWeek.Monday,
    Ter: DaysOfWeek.Tuesday,
    Qua: DaysOfWeek.Wednesday,
    Qui: DaysOfWeek.Thursday,
    Sex: DaysOfWeek.Friday,
    Sáb: DaysOfWeek.Saturday,
  };

  const dayBit = dayMap[day];
  return dayBit !== undefined && (bitmask & dayBit) !== 0;
}

// ==============================================
// UTILITY TYPES
// ==============================================

/**
 * Type for database operations result
 */
export interface DatabaseResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  affectedRows?: number;
}

/**
 * Type for synchronization operations
 */
export interface SyncOperation<T> {
  operation: "insert" | "update" | "delete";
  data: T;
}

/**
 * Type for batch operations
 */
export interface BatchOperation<T> extends SyncOperation<T> {
  batchId: string;
  timestamp: number;
}

/**
 * Configuration type for MQTT synchronization
 */
export interface MqttSyncConfig {
  topicPrefix: string;
  batchSize: number;
  retryAttempts: number;
  syncInterval: number;
}

/**
 * Type for device scheduling with extended information
 */
export interface ExtendedDeviceScheduling extends IrriganetDeviceScheduling {
  sector?: number;
  scheduling_ord_idx?: number;
  codec_identity?: string;
}
