import type { SQL } from "bun";
import { codec } from "proto";
import DB from "../db/connection";
import { getLICTree } from "../db/queries/lic-queries";
import { listPropertyLICS } from "../db/queries/property-device-queries";
import type {
  OmitAndMerge,
  ProjectWithSchedulingData,
  Property,
  PropertyDeviceWithDevice,
  PropertyDeviceWithDeviceAndProperty,
  SectorWithValveController,
  WaterPumpWithController,
} from "../db/queries/types";
import type {
  IrriganetCodec,
  IrriganetCodecConfig,
  IrriganetDevice,
  IrriganetGroup,
  IrriganetMeshDevice,
} from "./types";
import { getPropertyById } from "../db/queries/property-queries";
import { date } from "../utils/date";

const MESH_DEVICE_TYPES = {
  Valve: 0,
  Pump: 1,
  Level: 2,
};

const DEV_TYPES = {
  Valve: 0,
  IrrigationPump: 1,
  Ferti: 2,
  Backwash: 3,
  ServicePump: 4,
  Level: 5,
} as const;

const PULSE = 0x01 as const; //
const MONI = 0x02 as const; //

type LICTreeRaw = NonNullable<Awaited<ReturnType<typeof getLICTree>>>;
type LICTree = OmitAndMerge<
  LICTreeRaw,
  "propertyDevice",
  { propertyDevice: NonNullable<LICTreeRaw["propertyDevice"]> }
>;
type CodecConfig = IrriganetCodec & {
  propertyDeviceId: string;
  config: IrriganetCodecConfig;
};
type IrriganetGroupWithProjectId = IrriganetGroup & {
  projectId: string;
};
type IrriganetMeshDeviceWithDeviceId = IrriganetMeshDevice & {
  deviceId: string;
};

export async function loadPropertyState(
  db: SQL,
  propertyId: string,
  referenceDate: Date
) {
  const result: Array<{
    codec: CodecConfig;
    collections: Awaited<ReturnType<typeof loadLICState>>;
  }> = [];
  const property = await getPropertyById(db, propertyId);
  if (!property) {
    throw new Error(`Property not found: ${propertyId}`);
  }
  const propertyLICS = await listPropertyLICS(db, propertyId, referenceDate);
  for (let idx = 0; idx < propertyLICS.length; idx++) {
    const lic = propertyLICS[idx]!;
    const codec: CodecConfig = createCodecConfig(idx + 1, lic, property);
    const collections = await loadLICState(db, codec, referenceDate);
    result.push({
      codec,
      collections,
    });
  }
  return result;
}

export async function loadLICState(
  db: SQL,
  codec: CodecConfig,
  referenceDate: Date
) {
  const tree = await loadTree(db, codec.identity, referenceDate);
  const groups = generateGroups(tree);
  const { devices, meshDevices } = generateMeshDevices(
    tree,
    groups,
    codec,
    referenceDate
  );
  return {
    groups,
    devices,
    meshDevices,
  };
}

async function loadTree(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date
): Promise<LICTree> {
  const tree: LICTreeRaw | null = await getLICTree(
    db,
    licIdentifier,
    referenceDate
  );

  if (!tree) {
    throw new Error(`No tree found for LIC device ${licIdentifier}`);
  }

  if (!tree.propertyDevice) {
    throw new Error(`No property device found for LIC device ${licIdentifier}`);
  }

  return tree as LICTree;
}

function createCodecConfig(
  idx: number,
  lic: PropertyDeviceWithDevice,
  property: Property
): CodecConfig {
  return {
    idx,
    enabled: 1,
    identity: lic.device.identifier,
    name: lic.metadata?.label || lic.device.identifier,
    propertyDeviceId: lic.id,
    config: generateLICConfig(property, lic),
  };
}

function generateLICConfig(
  property: Property,
  propertyDevice: PropertyDeviceWithDevice
) {
  let wifiConfig: codec.in_.config.WifiConfig | undefined;

  // For LIC devices, get WiFi credentials from property_device metadata instead of device metadata
  const metadata = propertyDevice.metadata;

  if (metadata) {
    if (metadata.wifiSSID && metadata.wifiPassword) {
      wifiConfig = codec.in_.config.WifiConfig.create({
        ssid: metadata.wifiSSID,
        password: metadata.wifiPassword,
      });
    }
  }

  const config: IrriganetCodecConfig = {
    backwashCycle: property.backwash_period_minutes || 0,
    backwashDuration: property.backwash_duration_minutes || 0,
    backwashDelay: property.backwash_delay_seconds || 30, // Default delay of 30 seconds
    raingaugeEnabled: property.rain_gauge_enabled || false,
    raingaugeFactor: property.rain_gauge_resolution_mm
      ? Math.round(1.0 / property.rain_gauge_resolution_mm)
      : 5, // Default factor for 0.2mm resolution
    rainfallLimit: property.precipitation_volume_limit_mm || 2, // Default 2mm
    rainfallPauseDuration: property.precipitation_suspended_duration_hours
      ? property.precipitation_suspended_duration_hours * 60 // Convert hours to minutes
      : 1440, // Default 24 hours in minutes
    wifi: wifiConfig,
    // mesh configuration is not populated in the new system
    publishRawData: false, // As implemented in MainActivity.kt
    debug: false, // As implemented in MainActivity.kt
    enableScheduleResumption: false, // As implemented in MainActivity.kt
    enableFertiResumption: false, // As implemented in MainActivity.kt
    maxResumptionAttempts: 0, // As implemented in MainActivity.kt
  };
  return config;
}

function generateGroups(tree: LICTree): Array<IrriganetGroupWithProjectId> {
  return tree.projects.map((project, index) => ({
    idx: index + 1,
    name: project.name,
    projectId: project.id,
  }));
}

function generateMeshDevices(
  tree: LICTree,
  groups: Array<IrriganetGroupWithProjectId>,
  codec: CodecConfig,
  referenceDate: Date
) {
  const result: {
    meshDevices: IrriganetMeshDeviceWithDeviceId[];
    devices: IrriganetDevice[];
  } = {
    meshDevices: [],
    devices: [],
  };
  tree.projects.forEach((project) => {
    const group = groups.find((g) => g.projectId === project.id);
    if (!group) {
      throw new Error(`No group found for project ${project.id}`);
    }

    const { devices, meshDevices } = extractProjectDevices(
      codec,
      group,
      project,
      new IdGenerator(),
      new IdGenerator()
    );

    result.meshDevices.push(...meshDevices);
    result.devices.push(...devices);
  });

  return result;
}

function extractProjectDevices(
  codec: CodecConfig,
  group: IrriganetGroupWithProjectId,
  project: ProjectWithSchedulingData,
  meshDeviceIdGen: IdGenerator,
  deviceIdGen: IdGenerator
) {
  if (!project.irrigation_water_pump) {
    throw new Error("Invalid project - No irrigation pump");
  }
  if (!project.irrigation_water_pump.water_pump_controller) {
    throw new Error("Invalid project - No water pump controller");
  }
  const meshDevices: Array<IrriganetMeshDeviceWithDeviceId> = [];
  const devices: Array<IrriganetDevice> = [];

  // Irrigation water pump
  const irrigationPump = project.irrigation_water_pump;
  //TODO: Error here - "Invalid pump link model: undefined: undefined"
  const pumpLink = irrigationPump.water_pump_controller;

  if (pumpLink.model !== "WPC-PL10" && pumpLink.model !== "WPC-PL50") {
    throw new Error(
      `Invalid pump link model: ${pumpLink.identifier}: ${pumpLink.model}`
    );
  }

  const fertigationAndIrrigationControllerShared =
    project.fertigation_water_pump?.water_pump_controller?.id === pumpLink.id;

  const pumpMeshDevice = {
    idx: meshDeviceIdGen.next(),
    identity: pumpLink.identifier,
    name: irrigationPump.label || pumpLink.identifier,
    check_input: irrigationPump.monitor_operation ? 1 : 0,
    codec_idx: codec.idx,
    devices_bitmask: deviceBooleansToBitmask(
      true,
      fertigationAndIrrigationControllerShared,
      project.backwash_pump_type === "IRRIGATION",
      false
    ),
    equipament: pumpLink.model == "WPC-PL10" ? 0 : 1,
    group_idx: group.idx,
    level_pump_enable: 0,
    level_pump_idx: undefined,
    level_pump_working_time: 0,
    type: MESH_DEVICE_TYPES.Pump,
    mode: irrigationPump.mode === "CONTINUOUS" ? 0 : 1,
    deviceId: pumpLink.id,
  };
  meshDevices.push(pumpMeshDevice);

  // Add Irrigation Device
  addDevicesOfIrrigationMeshDevice(
    deviceIdGen,
    devices,
    pumpMeshDevice,
    project
  );

  if (
    project.fertigation_water_pump?.water_pump_controller &&
    !fertigationAndIrrigationControllerShared
  ) {
    const fertiPump = project.fertigation_water_pump;
    const fertiPumpLink = fertiPump.water_pump_controller;
    const fertiMeshDevice = {
      idx: meshDeviceIdGen.next(),
      identity: fertiPumpLink.identifier,
      name: fertiPump.label || fertiPumpLink.identifier,
      check_input: fertiPump.monitor_operation ? 1 : 0,
      codec_idx: codec.idx,
      devices_bitmask: deviceBooleansToBitmask(
        false,
        true,
        project.backwash_pump_type === "FERTIGATION",
        false
      ),
      equipament: fertiPumpLink.model == "WPC-PL10" ? 0 : 1,
      group_idx: group.idx,
      level_pump_enable: 0,
      level_pump_idx: undefined,
      level_pump_working_time: 0,
      type: MESH_DEVICE_TYPES.Pump,
      mode: fertiPump.mode === "CONTINUOUS" ? 0 : 1,
      deviceId: fertiPumpLink.id,
    };
    meshDevices.push(fertiMeshDevice);
    addDevicesOfFertiMeshDevice(deviceIdGen, devices, fertiMeshDevice, project);
  }

  const groupedSectors = Object.groupBy(
    project.sectors,
    (sector) => sector.valve_controller
  );
  Object.values(groupedSectors)
    .toSorted(
      (a, b) =>
        (date(a?.at(0)?.valve_controller_device.date_created)?.getTime() ?? 0) -
        (date(b?.at(0)?.valve_controller_device.date_created)?.getTime() ?? 0)
    )
    .forEach((sectors) => {
      if (!sectors || sectors.length === 0) return;
      const firstSector = sectors[0]!;
      // Create mesh device for the first sector
      const meshDevice = {
        idx: meshDeviceIdGen.next(),
        identity: firstSector.valve_controller_device.identifier,
        name:
          firstSector.name || firstSector.valve_controller_device.identifier,
        check_input: 0,
        codec_idx: codec.idx,
        devices_bitmask: 0,
        equipament: 0,
        group_idx: group.idx,
        level_pump_enable: 0,
        level_pump_idx: undefined,
        level_pump_working_time: 0,
        type: MESH_DEVICE_TYPES.Valve,
        mode: 0,
        deviceId: firstSector.valve_controller_device.id,
      };
      meshDevices.push(meshDevice);
      addDevicesOfValveMeshDevice(
        deviceIdGen,
        devices,
        meshDevice,
        project,
        sectors.toSorted((a, b) => a.name.localeCompare(b.name))
      );
    });

  return { meshDevices, devices };
}

function addDevicesOfIrrigationMeshDevice(
  deviceIdGen: IdGenerator,
  devices: IrriganetDevice[],
  pumpMeshDevice: IrriganetMeshDeviceWithDeviceId,
  project: ProjectWithSchedulingData
) {
  const irrigationPump = project.irrigation_water_pump;
  const fertigationAndIrrigationControllerShared =
    project.fertigation_water_pump?.water_pump_controller?.id ===
    pumpMeshDevice.deviceId;

  if (irrigationPump.water_pump_controller.model === "WPC-PL10") {
    const irrigDeviceIdx = deviceIdGen.next();
    devices.push({
      idx: irrigDeviceIdx,
      mesh_idx: pumpMeshDevice.idx,
      identity: "0",
      type: DEV_TYPES.IrrigationPump,
      out1: 1,
      out2: 0,
      input: pumpMeshDevice.check_input ? 1 : 0,
      mode:
        (irrigationPump.mode === "PULSE" ? PULSE : 0) |
        (irrigationPump.monitor_operation ? MONI : 0),
      sector: undefined,
      ord_idx: irrigDeviceIdx - 1,
      eqpt_ver: undefined,
      power: undefined,
    });

    // If fertigation and irrigation controller are shared, add Ferti device for the same meshDevice
    if (fertigationAndIrrigationControllerShared) {
      /*
                            val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "1",
                                "type" to DevType.Ferti.value,
                                "out1" to "2",
                                "out2" to "0",
                                "input" to "0",
                                "mode" to 0,
                                "sector" to null
                            )
    */
      const fertiDeviceIdx = deviceIdGen.next();
      devices.push({
        idx: fertiDeviceIdx,
        mesh_idx: pumpMeshDevice.idx,
        identity: "1",
        type: DEV_TYPES.Ferti,
        out1: 2,
        out2: 0,
        input: 0,
        mode: 0,
        sector: undefined,
        ord_idx: fertiDeviceIdx - 1,
        eqpt_ver: undefined,
        power: undefined,
      });
    }

    // If backwash pump type is irrigation, add Backwash device for the same meshDevice
    if (project.backwash_pump_type === "IRRIGATION") {
      /*
     val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "2",
                                "type" to DevType.Backwash.value,
                                "out1" to "3",
                                "out2" to "0",
                                "input" to "0",
                                "mode" to 0,
                                "sector" to null
                            )
    */
      const backwashDeviceIdx = deviceIdGen.next();
      devices.push({
        idx: backwashDeviceIdx,
        mesh_idx: pumpMeshDevice.idx,
        identity: "2",
        type: DEV_TYPES.Backwash,
        out1: 3,
        out2: 0,
        input: 0,
        mode: 0,
        sector: undefined,
        ord_idx: backwashDeviceIdx - 1,
        eqpt_ver: undefined,
        power: undefined,
      });
    }
  } else {
    /*
     val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "0",
                                "type" to DevType.IrrigationPump.value,
                                "out1" to "1",
                                "out2" to if (binding.fragMeshDeviceRadioPulse.isChecked) "2" else "0",
                                "input" to input,
                                "mode" to mode,
                                "sector" to null
                            )
    */
    const irrigDeviceIdx = deviceIdGen.next();
    devices.push({
      idx: irrigDeviceIdx,
      mesh_idx: pumpMeshDevice.idx,
      identity: "0",
      type: DEV_TYPES.IrrigationPump,
      out1: 1,
      out2: irrigationPump.mode === "PULSE" ? 2 : 0,
      input: pumpMeshDevice.check_input ? 1 : 0,
      mode:
        (irrigationPump.mode === "PULSE" ? PULSE : 0) |
        (irrigationPump.monitor_operation ? MONI : 0),
      sector: undefined,
      ord_idx: irrigDeviceIdx - 1,
      eqpt_ver: undefined,
      power: undefined,
    });

    // PL50 Ferti  only on mode CONTINUOUS
    if (
      fertigationAndIrrigationControllerShared &&
      irrigationPump.mode === "CONTINUOUS"
    ) {
      /*
      val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "1",
                                "type" to DevType.Ferti.value,
                                "out1" to "2",
                                "out2" to "0",
                                "input" to "0",
                                "mode" to 0,
                                "sector" to null
                            )
      */
      const fertiDeviceIdx = deviceIdGen.next();
      devices.push({
        idx: fertiDeviceIdx,
        mesh_idx: pumpMeshDevice.idx,
        identity: "1",
        type: DEV_TYPES.Ferti,
        out1: 2,
        out2: 0,
        input: 0,
        mode: 0,
        sector: undefined,
        ord_idx: fertiDeviceIdx - 1,
        eqpt_ver: undefined,
        power: undefined,
      });
    }

    // PL50 has no backwash pump
  }
}

function addDevicesOfFertiMeshDevice(
  deviceIdGen: IdGenerator,
  devices: IrriganetDevice[],
  fertiMeshDevice: IrriganetMeshDeviceWithDeviceId,
  project: ProjectWithSchedulingData
) {
  const fertiPump = project.fertigation_water_pump;
  if (!fertiPump) {
    return;
  }
  const fertiPumpLink = fertiPump.water_pump_controller;

  // PL10 or PL50 on mode CONTINUOUS
  if (fertiPumpLink.model === "WPC-PL10" || fertiPump.mode === "CONTINUOUS") {
    const fertiDeviceIdx = deviceIdGen.next();
    devices.push({
      idx: fertiDeviceIdx,
      mesh_idx: fertiMeshDevice.idx,
      identity: "1",
      type: DEV_TYPES.Ferti,
      out1: 2,
      out2: 0,
      input: 0,
      mode: 0,
      sector: undefined,
      ord_idx: fertiDeviceIdx - 1,
      eqpt_ver: undefined,
      power: undefined,
    });
  }

  // Backwash device only for PL10
  if (
    project.backwash_pump_type === "FERTIGATION" &&
    fertiPumpLink.model === "WPC-PL10"
  ) {
    const backwashDeviceIdx = deviceIdGen.next();
    devices.push({
      idx: backwashDeviceIdx,
      mesh_idx: fertiMeshDevice.idx,
      identity: "2",
      type: DEV_TYPES.Backwash,
      out1: 3,
      out2: 0,
      input: 0,
      mode: 0,
      sector: undefined,
      ord_idx: backwashDeviceIdx - 1,
      eqpt_ver: undefined,
      power: undefined,
    });
  }
}

function addDevicesOfValveMeshDevice(
  deviceIdGen: IdGenerator,
  devices: IrriganetDevice[],
  meshDevice: IrriganetMeshDeviceWithDeviceId,
  project: ProjectWithSchedulingData,
  sectors: SectorWithValveController[]
) {
  sectors.forEach((sector, index) => {
    const valveDeviceIdx = deviceIdGen.next();
    /*
    val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to index.toString(),
                                "type" to MainType.Valve.value,
                                "out1" to (2*index + 1).toString(),
                                "out2" to (2*index + 2).toString(),
                                "input" to "0",
                                "mode" to PULSE,
                                "sector" to sector.toString()
                            )
    */
    devices.push({
      idx: valveDeviceIdx,
      mesh_idx: meshDevice.idx,
      identity: index.toString(),
      type: DEV_TYPES.Valve,
      out1: 2 * index + 1,
      out2: 2 * index + 2,
      input: 0,
      mode: PULSE,
      sector: index + 1,
      ord_idx: valveDeviceIdx - 1,
      eqpt_ver: undefined,
      power: project.irrigation_water_pump.has_frequency_inverter
        ? sector.power || 0
        : 0,
    });
  });
}

/**
 * Convert device presence booleans to a bitmask using DEV_TYPES values.
 */
function deviceBooleansToBitmask(
  irrigationPump: boolean,
  ferti: boolean,
  backwash: boolean,
  individualPump: boolean
): number {
  let bitmask = 0;
  if (irrigationPump) bitmask |= 1 << DEV_TYPES.IrrigationPump;
  if (ferti) bitmask |= 1 << DEV_TYPES.Ferti;
  if (backwash) bitmask |= 1 << DEV_TYPES.Backwash;
  if (individualPump) bitmask |= 1 << DEV_TYPES.ServicePump;
  return bitmask;
}

class IdGenerator {
  private currentId = 0;

  getCurrentId() {
    return this.currentId;
  }

  next() {
    return this.currentId++;
  }
}

if (import.meta.main) {
  loadPropertyState(DB, "aca444ac-1e4a-405f-a6d2-e33a70712a59", new Date())
    .then((config) => {
      console.log(JSON.stringify(config, null, 2));
    })
    .catch((error) => {
      console.error("Error building config:", error);
    });
}
