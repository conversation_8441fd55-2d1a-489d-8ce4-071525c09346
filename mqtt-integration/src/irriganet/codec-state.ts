import type { SQL } from "bun";
import type {
  IrriganetDevice,
  IrriganetCodec,
  IrriganetGroup,
  IrriganetMeshDevice,
  IrriganetScheduling,
  IrriganetSectorScheduling,
  IrriganetDeviceScheduling,
  IrriganetCodecConfig,
} from "./types";
import { loadLICState } from "./db-loaders";

export interface CodecState extends IrriganetCodec {
  get groups(): Array<
    IrriganetGroup & {
      projectId: string;
    }
  >;
  get meshDevices(): Array<IrriganetMeshDevice>;
  get devices(): Array<IrriganetDevice>;
  get schedulings(): Array<IrriganetScheduling>;
  get sectorSchedulings(): Array<IrriganetSectorScheduling>;
  get deviceSchedulings(): Array<IrriganetDeviceScheduling>;
  get config(): IrriganetCodecConfig;
}

export class CodecStateManager {
  constructor(
    private licIdentifier: string,
    private referenceDate: Date,
    private state: CodecState
  ) {}

  getState(): CodecState {
    return this.state;
  }

  getLicIdentifier() {
    return this.licIdentifier;
  }

  getReferenceDate() {
    return this.referenceDate;
  }

  updateState(newState: Partial<CodecState>): void {
    this.state = { ...this.state, ...newState };
  }

  static async loadFromDB(db: SQL, licIdentifier: string, referenceDate: Date) {
    // const config = await loadConfig(db, licIdentifier, referenceDate);
  }
}
