# Android to Irriga Mais Scheduling Data Mapping

This document provides comprehensive mapping between the Android IrrigaNet app's SQLite schema and the Irriga Mais PostgreSQL schema for building SchedulingPackage protobuf messages.

## Overview

The Android app builds `SchedulingPackage` messages containing two main components:
1. **Scheduling data**: Main irrigation plans with timing and configuration
2. **DeviceScheduling data**: Individual device assignments within each plan

## Android App Schema Analysis

### Key Android Tables

#### Schedulings Table
```sql
CREATE TABLE schedulings (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    ord_idx INTEGER NOT NULL,           -- Order index for protocol
    group_idx INTEGER NOT NULL,         -- FK to groups table
    name TEXT NOT NULL,
    hour INTEGER NOT NULL,
    min INTEGER NOT NULL,
    start_time INTEGER NOT NULL,        -- Minutes since midnight
    end_time INTEGER DEFAULT NULL,
    days_of_week INTEGER NOT NULL,      -- Bitmask (0b01111111)
    number_of_steps INTEGER NOT NULL,
    allow_ferti BOOLEAN NOT NULL,
    allow_backwash BOOLEAN NOT NULL,
    waterpump_ord_idx INTEGER,
    waterpump_working_time INTEGER,
    ferti_ord_idx INTEGER,
    backwash_ord_idx INTEGER,
    enabled BOOLEAN NOT NULL
);
```

#### Device_Schedulings Table  
```sql
CREATE TABLE device_schedulings (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    ord_idx INTEGER NOT NULL,           -- Order index for protocol
    scheduling_idx INTEGER NOT NULL,    -- FK to schedulings
    device_idx INTEGER NOT NULL,        -- FK to devices
    n_order INTEGER NOT NULL,           -- Execution order within schedule
    status INTEGER NOT NULL,
    type INTEGER NOT NULL,
    time INTEGER NOT NULL,
    sector_working_time INTEGER NOT NULL,  -- Duration in seconds
    ferti_working_time INTEGER NOT NULL,   -- Duration in seconds
    ferti_delay INTEGER NOT NULL          -- Delay in seconds
);
```

## Irriga Mais Schema Analysis

### Key Irriga Mais Tables

#### irrigation_plan Table
```sql
CREATE TABLE irrigation_plan (
    id uuid PRIMARY KEY,
    project uuid NOT NULL,              -- Maps to Android group concept
    name varchar(255) NOT NULL,
    description text,
    start_time time NOT NULL,            -- Time without timezone
    days_of_week jsonb NOT NULL,         -- Array like ["MON", "TUE", ...]
    is_enabled boolean NOT NULL,
    fertigation_enabled boolean NOT NULL,
    total_irrigation_duration integer,
    start_date date,
    end_date date
);
```

#### irrigation_plan_step Table
```sql
CREATE TABLE irrigation_plan_step (
    id uuid PRIMARY KEY,
    irrigation_plan uuid NOT NULL,      -- FK to irrigation_plan
    sector uuid NOT NULL,               -- FK to sector
    "order" integer NOT NULL,           -- Execution order
    duration_seconds integer NOT NULL,  -- Sector working time
    fertigation_start_delay_seconds integer,  -- Ferti delay
    fertigation_duration_seconds integer,     -- Ferti working time
    description text
);
```

## Data Mapping Rules

### Scheduling Message Mapping

| Protobuf Field | Android Field | Irriga Mais Field | Conversion Notes |
|----------------|---------------|-------------------|------------------|
| `idx` | `ord_idx` | Generated sequence | Sequential numbering per LIC |
| `start_time` | `start_time` | `start_time` | Minutes since midnight |
| `days_of_week` | `days_of_week` | `days_of_week` | Bitmask conversion needed |
| `number_of_steps` | `number_of_steps` | Count of `irrigation_plan_step` | Calculate from steps |
| `waterpump_idx` | `waterpump_ord_idx` | Irrigation pump device ord_idx | From project pump |
| `waterpump_working_time` | `waterpump_working_time` | Sum of step durations | Calculate total duration |
| `allow_ferti` | `allow_ferti` | `fertigation_enabled` | Boolean mapping |
| `allow_backwash` | `allow_backwash` | Project `backwash_pump_type != null` | Derive from project config |
| `ferti_idx` | `ferti_ord_idx` | Fertigation pump device ord_idx | From project ferti pump |
| `backwash_idx` | `backwash_ord_idx` | Backwash pump device ord_idx | From project backwash config |
| `group` | `group_idx` | Project sequence | Project order in LIC |
| `once` | `once` | `end_date IS NOT NULL` | Single execution flag |

### DeviceScheduling Message Mapping

| Protobuf Field | Android Field | Irriga Mais Field | Conversion Notes |
|----------------|---------------|-------------------|------------------|
| `idx` | `ord_idx` | Generated sequence | Sequential numbering per LIC |
| `scheduling_idx` | `scheduling_ord_idx` | Parent plan ord_idx | Reference to parent plan |
| `device_idx` | `device_ord_idx` | Valve device ord_idx | From sector valve controller |
| `order` | `n_order` | `order` | Step execution order |
| `sector_working_time` | `sector_working_time` | `duration_seconds` | Direct mapping |
| `ferti_working_time` | `ferti_working_time` | `fertigation_duration_seconds` | Direct mapping |
| `ferti_delay` | `ferti_delay` | `fertigation_start_delay_seconds` | Direct mapping |

## Days of Week Conversion

### Android Bitmask Format
- Uses integer bitmask where each bit represents a day
- Bit 0 = Sunday, Bit 1 = Monday, ..., Bit 6 = Saturday
- Example: `0b01111111` = all days enabled

### Irriga Mais JSON Array Format
- Uses JSON array of 3-letter day codes
- Format: `["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"]`

### Conversion Function
```typescript
function convertDaysOfWeek(irrigaMaisDays: string[]): number {
  const dayMap = {
    'SUN': 0, 'MON': 1, 'TUE': 2, 'WED': 3, 
    'THU': 4, 'FRI': 5, 'SAT': 6
  };
  return irrigaMaisDays.reduce((bitmask, day) => 
    bitmask | (1 << dayMap[day]), 0);
}
```

## Key Concepts Mapping

### Group Concept
- **Android**: `group_idx` represents a logical grouping of devices under a codec
- **Irriga Mais**: `project` serves the same purpose - groups sectors under a LIC

### Device Order Index (ord_idx)
- **Android**: Sequential numbering for protocol communication
- **Irriga Mais**: Must be calculated based on device order within LIC context
- **Implementation**: Use existing `devices.ts` virtual device logic

### Time Calculations ⚠️ CRITICAL
- **Android**: `start_time` in **seconds** since midnight `(hour * 3600) + (minute * 60)`
- **Irriga Mais**: `time` field must be converted to seconds
- **Device Scheduling Time**: Accumulates with 30-second gaps between sectors
- **Working Time**: Calculate from enabled steps + gaps, convert to minutes for pumps

### Android App Critical Logic (from SectorSchedulingFragment.kt)

#### Time Accumulation Logic:
```kotlin
val startTime: Int = (hour.toInt() * 3600) + (min.toInt() * 60)  // SECONDS not minutes!
var time: Int = startTime
var elapsedTime: Int = 0

// For each enabled sector:
elapsedTime += (working_time_seconds)
time += elapsedTime
if (not_last_sector) {
    elapsedTime += 30  // 30-second gap
    time += 30
}

// Pump working time calculation:
waterpumpWorkingTime = elapsedTime / 60  // Convert seconds to minutes
```

#### Device Scheduling Creation:
- Only enabled sectors (duration > 0) get device scheduling records
- `n_order` is sequential counter (`reorder`) for enabled devices only
- Each device scheduling has accumulated `time` value
- Gaps between sectors are exactly 30 seconds

## Implementation Strategy

1. **Query Pattern**: 
   - Start with LIC identifier
   - Find all projects for that LIC
   - For each project, find enabled irrigation plans
   - For each plan, find irrigation plan steps with sectors

2. **Device Resolution**:
   - Use existing device mapping from `devices.ts`
   - Generate consistent ord_idx values
   - Map sectors to valve devices
   - Map pumps to appropriate device indices

3. **Message Construction**:
   - Build Scheduling messages first (main plans)
   - Build DeviceScheduling messages second (steps)
   - Use proper sequence numbering for ord_idx fields

## Database Query Requirements

The implementation will need queries to:
1. Get all projects for a LIC identifier
2. Get enabled irrigation plans for each project
3. Get irrigation plan steps with sector and device info
4. Calculate device ord_idx mappings
5. Resolve pump configurations for ferti/backwash