import type { AUTProperty, AUTWaterPump, AUTReservoir, AUTProject, AUTSector } from "@/api/queries/account";
import type { DeviceModel } from "@/api/model/device";

/**
 * Device association information
 */
export interface DeviceAssociation {
  type: 'project' | 'water_pump' | 'reservoir' | 'sector';
  name: string;
  id: string;
  description?: string;
}

/**
 * Sector-project association for valve controllers
 */
export interface SectorProjectAssociation {
  sectorName: string;
  sectorId: string;
  projectName: string;
  projectId: string;
}

/**
 * Find all associations for a device based on its ID and model
 * @param deviceId - The device UUID to find associations for
 * @param deviceModel - The device model type
 * @param property - The property containing all related data
 * @returns Array of associations for the device
 */
export function findDeviceAssociations(
  deviceId: string,
  deviceModel: DeviceModel,
  property: AUTProperty | null
): DeviceAssociation[] {
  if (!property) return [];

  const associations: DeviceAssociation[] = [];

  switch (deviceModel) {
    case 'LIC':
      // Find projects where this LIC is the localized irrigation controller
      const licProjects = property.projects.filter(
        project => project.localized_irrigation_controller === deviceId
      );
      associations.push(...licProjects.map(project => ({
        type: 'project' as const,
        name: project.name,
        id: project.id,
        description: project.description || undefined,
      })));
      break;

    case 'WPC-PL10':
    case 'WPC-PL50':
      // Find water pumps controlled by this WPC
      const controlledPumps = property.water_pumps.filter(
        pump => pump.water_pump_controller === deviceId
      );
      associations.push(...controlledPumps.map(pump => ({
        type: 'water_pump' as const,
        name: pump.label,
        id: pump.id,
      })));
      break;

    case 'RM':
      // Find reservoirs monitored by this RM
      const monitoredReservoirs = property.reservoirs.filter(
        reservoir => reservoir.reservoir_monitor === deviceId
      );
      associations.push(...monitoredReservoirs.map(reservoir => ({
        type: 'reservoir' as const,
        name: reservoir.name,
        id: reservoir.id,
        description: reservoir.description || undefined,
      })));

      // Also find water pumps connected to those reservoirs
      for (const reservoir of monitoredReservoirs) {
        if (reservoir.water_pump) {
          const waterPump = property.water_pumps.find(
            pump => pump.id === reservoir.water_pump
          );
          if (waterPump) {
            associations.push({
              type: 'water_pump' as const,
              name: `${waterPump.label} (via ${reservoir.name})`,
              id: waterPump.id,
            });
          }
        }
      }
      break;

    case 'VC':
      // Find sectors controlled by this VC
      const controlledSectors: AUTSector[] = [];
      property.projects.forEach(project => {
        if (project.sectors) {
          const sectors = project.sectors.filter(
            sector => sector.valve_controller === deviceId
          );
          controlledSectors.push(...sectors);
        }
      });
      
      associations.push(...controlledSectors.map(sector => ({
        type: 'sector' as const,
        name: sector.name,
        id: sector.id,
        description: sector.description || undefined,
      })));
      break;
  }

  return associations;
}

/**
 * Format device associations for display
 * @param associations - Array of device associations
 * @returns Formatted association text or null if no associations
 */
export function formatDeviceAssociations(associations: DeviceAssociation[]): string | null {
  if (associations.length === 0) return null;

  if (associations.length === 1) {
    const assoc = associations[0];
    const typeLabels = {
      project: 'Projeto',
      water_pump: 'Bomba',
      reservoir: 'Reservatório',
      sector: 'Setor',
    };
    return `${typeLabels[assoc.type]}: ${assoc.name}`;
  }

  // Group associations by type for multiple associations
  const grouped = associations.reduce((acc, assoc) => {
    if (!acc[assoc.type]) acc[assoc.type] = [];
    acc[assoc.type].push(assoc.name);
    return acc;
  }, {} as Record<string, string[]>);

  const parts: string[] = [];
  const typeLabels = {
    project: 'Projetos',
    water_pump: 'Bombas',
    reservoir: 'Reservatórios',
    sector: 'Setores',
  };

  for (const [type, names] of Object.entries(grouped)) {
    const label = typeLabels[type as keyof typeof typeLabels];
    if (names.length === 1) {
      parts.push(`${label.slice(0, -1)}: ${names[0]}`); // Singular form
    } else {
      parts.push(`${label}: ${names.join(', ')}`);
    }
  }

  return parts.join(' • ');
}

/**
 * Get association icon based on association type
 * @param type - The association type
 * @returns Emoji icon for the association type
 */
export function getAssociationIcon(type: DeviceAssociation['type']): string {
  const icons = {
    project: '🌱',
    water_pump: '💧',
    reservoir: '🏗️',
    sector: '🔧',
  };
  return icons[type];
}

/**
 * Find all associations for a water pump based on its ID
 * @param pumpId - The water pump UUID to find associations for
 * @param property - The property containing all related data
 * @returns Array of associations for the water pump
 */
export function findWaterPumpAssociations(
  pumpId: string,
  property: AUTProperty | null
): DeviceAssociation[] {
  if (!property) return [];

  const associations: DeviceAssociation[] = [];

  // Find projects where this water pump is used for irrigation
  const irrigationProjects = property.projects.filter(
    project => project.irrigation_water_pump === pumpId
  );
  associations.push(...irrigationProjects.map(project => ({
    type: 'project' as const,
    name: `${project.name} (Irrigação)`,
    id: project.id,
    description: project.description || undefined,
  })));

  // Find projects where this water pump is used for fertigation
  const fertigationProjects = property.projects.filter(
    project => project.fertigation_water_pump === pumpId
  );
  associations.push(...fertigationProjects.map(project => ({
    type: 'project' as const,
    name: `${project.name} (Fertirrigação)`,
    id: project.id,
    description: project.description || undefined,
  })));

  // Find reservoirs where this water pump is connected
  const connectedReservoirs = property.reservoirs.filter(
    reservoir => reservoir.water_pump === pumpId
  );
  associations.push(...connectedReservoirs.map(reservoir => ({
    type: 'reservoir' as const,
    name: reservoir.name,
    id: reservoir.id,
    description: reservoir.description || undefined,
  })));

  return associations;
}

/**
 * Check if a device has any associations
 * @param deviceId - The device UUID
 * @param deviceModel - The device model type
 * @param property - The property containing all related data
 * @returns True if the device has associations, false otherwise
 */
export function hasDeviceAssociations(
  deviceId: string,
  deviceModel: DeviceModel,
  property: AUTProperty | null
): boolean {
  const associations = findDeviceAssociations(deviceId, deviceModel, property);
  return associations.length > 0;
}

/**
 * Find sector-project associations for a valve controller
 * @param deviceId - The valve controller UUID to find associations for
 * @param property - The property containing all related data
 * @returns Array of sector-project associations
 */
export function findValveControllerSectorProjectAssociations(
  deviceId: string,
  property: AUTProperty | null
): SectorProjectAssociation[] {
  if (!property) return [];

  const associations: SectorProjectAssociation[] = [];

  property.projects.forEach(project => {
    if (project.sectors) {
      const sectors = project.sectors.filter(
        sector => sector.valve_controller === deviceId
      );
      
      associations.push(...sectors.map(sector => ({
        sectorName: sector.name,
        sectorId: sector.id,
        projectName: project.name,
        projectId: project.id,
      })));
    }
  });

  return associations;
}

/**
 * Format valve controller sector-project associations for display in Portuguese
 * @param associations - Array of sector-project associations
 * @returns Formatted text or null if no associations
 */
export function formatValveControllerAssociations(associations: SectorProjectAssociation[]): string | null {
  if (associations.length === 0) return null;

  // Group sectors by project
  const grouped = associations.reduce((acc, assoc) => {
    if (!acc[assoc.projectId]) {
      acc[assoc.projectId] = {
        projectName: assoc.projectName,
        sectors: []
      };
    }
    acc[assoc.projectId].sectors.push(assoc.sectorName);
    return acc;
  }, {} as Record<string, { projectName: string; sectors: string[] }>);

  const parts: string[] = [];
  
  for (const projectGroup of Object.values(grouped)) {
    const sectors = projectGroup.sectors;
    let sectorText: string;
    
    if (sectors.length === 1) {
      sectorText = sectors[0];
    } else if (sectors.length === 2) {
      sectorText = `${sectors[0]} e ${sectors[1]}`;
    } else {
      const lastSector = sectors[sectors.length - 1];
      const otherSectors = sectors.slice(0, -1).join(', ');
      sectorText = `${otherSectors} e ${lastSector}`;
    }
    
    parts.push(`${sectorText} em ${projectGroup.projectName}`);
  }
  
  if (parts.length === 1) {
    return parts[0];
  } else if (parts.length === 2) {
    return `${parts[0]} e ${parts[1]}`;
  } else {
    const lastPart = parts[parts.length - 1];
    const otherParts = parts.slice(0, -1).join(', ');
    return `${otherParts} e ${lastPart}`;
  }
}

/**
 * Check if a water pump has any associations
 * @param pumpId - The water pump UUID
 * @param property - The property containing all related data
 * @returns True if the water pump has associations, false otherwise
 */
export function hasWaterPumpAssociations(
  pumpId: string,
  property: AUTProperty | null
): boolean {
  const associations = findWaterPumpAssociations(pumpId, property);
  return associations.length > 0;
}