import { useMemo, useState } from 'react';
import type { AUTWaterPump } from '@/api/queries/account';
import type { DeviceWithMapping } from '@/utils/mesh-device-utils';

export function useSearch() {
  const [searchQuery, setSearchQuery] = useState('');

  const filterDevices = useMemo(() => {
    return (devices: DeviceWithMapping[]) => {
      if (!searchQuery.trim()) return devices;
      
      return devices.filter(
        (device) =>
          device.device.identifier
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          device.device.model.toLowerCase().includes(searchQuery.toLowerCase())
      );
    };
  }, [searchQuery]);

  const filterPumps = useMemo(() => {
    return (pumps: AUTWaterPump[]) => {
      if (!searchQuery.trim()) return pumps;
      
      return pumps.filter(
        (pump) =>
          pump.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
          pump.identifier.toLowerCase().includes(searchQuery.toLowerCase()) ||
          pump.pump_model.toLowerCase().includes(searchQuery.toLowerCase())
      );
    };
  }, [searchQuery]);

  return {
    searchQuery,
    setSearchQuery,
    filterDevices,
    filterPumps,
  };
}