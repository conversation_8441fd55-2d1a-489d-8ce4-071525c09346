import { useMemo, useState } from 'react';
import type { DeviceFilter } from '@/pages/main/components/DeviceFilterBar';
import type { DeviceWithMapping } from '@/utils/mesh-device-utils';

export function useDeviceFiltering(devices: DeviceWithMapping[]) {
  const [deviceFilter, setDeviceFilter] = useState<DeviceFilter>('all');

  const filteredDevices = useMemo(() => {
    switch (deviceFilter) {
      case 'coordinators':
        return devices.filter((d) => d.mappingStatus === 'coordinator');
      case 'mapped':
        return devices.filter((d) => d.mappingStatus === 'mapped');
      case 'unmapped':
        return devices.filter((d) => d.mappingStatus === 'unmapped');
      case 'lic':
        return devices.filter((d) => d.device.model === 'LIC');
      case 'wpc':
        return devices.filter((d) => d.device.model.startsWith('WPC'));
      case 'vc':
        return devices.filter((d) => d.device.model === 'VC');
      case 'rm':
        return devices.filter((d) => d.device.model === 'RM');
      default:
        return devices;
    }
  }, [devices, deviceFilter]);

  const deviceCounts = useMemo(() => {
    return {
      all: devices.length,
      coordinators: devices.filter((d) => d.mappingStatus === 'coordinator').length,
      mapped: devices.filter((d) => d.mappingStatus === 'mapped').length,
      unmapped: devices.filter((d) => d.mappingStatus === 'unmapped').length,
      lic: devices.filter((d) => d.device.model === 'LIC').length,
      wpc: devices.filter((d) => d.device.model.startsWith('WPC')).length,
      vc: devices.filter((d) => d.device.model === 'VC').length,
      rm: devices.filter((d) => d.device.model === 'RM').length,
    };
  }, [devices]);

  return {
    deviceFilter,
    setDeviceFilter,
    filteredDevices,
    deviceCounts,
  };
}