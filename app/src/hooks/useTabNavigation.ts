import { useSearchParams } from 'wouter';

const HARDWARE_TABS = ['devices', 'pumps'] as const;
type HardwareTab = typeof HARDWARE_TABS[number];

export function useTabNavigation(defaultTab: HardwareTab = 'devices') {
  const [searchParams, setSearchParams] = useSearchParams();
  
  const activeTab = searchParams.get('activeTab') as HardwareTab;
  const validActiveTab = activeTab && HARDWARE_TABS.includes(activeTab) ? activeTab : defaultTab;

  const setActiveTab = (tab: string) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set('activeTab', tab);
      return newParams;
    });
  };

  return {
    activeTab: validActiveTab,
    setActiveTab,
  };
}