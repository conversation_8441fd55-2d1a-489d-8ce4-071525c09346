import { hasActiveLoading<PERSON><PERSON> } from "@/store";
import { useAtomValue } from "jotai";
import { Clock, ListChecks, Plus, Settings } from "lucide-react";
import { useMemo } from "react";
import { useLocation } from "wouter";

import { DAY_OF_WEEK_VALUES, DayOfWeek } from "@/api/model/common";
import { AUTIrrigationPlan } from "@/api/queries/account";
import {
  irrigationPlansByProjectIdAtom,
  projectByIdAtom,
  sectorsByProjectIdAtom,
  selectedPropertyAtom,
} from "@/store/data";
import { formatDuration } from "./components/IrrigationPlanStepsPanel2/utils/formatters";
import { FAB } from "@/components/ui/FAB";

export type ProjectDetailPageProps = {
  projectId: string;
};

function ProjectDetailPage({ projectId }: ProjectDetailPageProps) {
  // Função para exibir os dias da semana em português, detectando "dias úteis" e "fim de semana" para valores abreviados (MON, TUE, ...)
  const formatDays = (days: DayOfWeek[]) => {
    const dayMap: Record<string, string> = {
      MON: "Seg",
      TUE: "Ter",
      WED: "Qua",
      THU: "Qui",
      FRI: "Sex",
      SAT: "Sáb",
      SUN: "Dom",
    };
    const allDays = [...DAY_OF_WEEK_VALUES];
    const weekdays: DayOfWeek[] = ["MON", "TUE", "WED", "THU", "FRI"];
    const weekend: DayOfWeek[] = ["SAT", "SUN"];

    // Ordena os dias conforme a semana
    const sortedDays = days
      .slice()
      .sort((a, b) => allDays.indexOf(a) - allDays.indexOf(b));
    const isWeekdays =
      weekdays.every((d) => sortedDays.includes(d)) && sortedDays.length === 5;
    const isWeekend =
      weekend.every((d) => sortedDays.includes(d)) && sortedDays.length === 2;
    const isEveryday =
      allDays.every((d) => sortedDays.includes(d)) && sortedDays.length === 7;

    if (isEveryday) return "Todos os dias";
    if (isWeekdays) return "Dias úteis";
    if (isWeekend) return "Fim de semana";

    return sortedDays.map((day) => dayMap[day] || day).join(", ");
  };

  const [, setLocation] = useLocation();

  // Store atoms
  const projectById = useAtomValue(projectByIdAtom);
  const irrigationPlansByProjectId = useAtomValue(
    irrigationPlansByProjectIdAtom
  );
  const sectorsByProjectId = useAtomValue(sectorsByProjectIdAtom);
  const property = useAtomValue(selectedPropertyAtom);
  const isLoading = useAtomValue(hasActiveLoadingAtom);

  const project = useMemo(() => {
    return projectById(projectId);
  }, [projectById, projectId]);
  const irrigationPlans = useMemo(() => {
    return irrigationPlansByProjectId(projectId);
  }, [irrigationPlansByProjectId, projectId]);
  const sectors = useMemo(() => {
    return sectorsByProjectId(projectId);
  }, [sectorsByProjectId, projectId]);

  // Helper functions to calculate derived values
  const getTotalArea = () => {
    if (!sectors) return "N/A";
    const totalArea = sectors.reduce(
      (sum, sector) => sum + (sector.area || 0),
      0
    );
    return totalArea > 0 ? `${totalArea.toFixed(1)}` : "N/A";
  };

  const hasIrrigation = () => {
    return !!project?.irrigation_water_pump;
  };

  const hasFertigation = () => {
    return !!project?.fertigation_water_pump;
  };

  const getNextExecutionText = (plan: AUTIrrigationPlan) => {
    // Since we don't have status/execution info in the API data,
    // we'll show the start time and days
    const days = plan.days_of_week.join(", ");
    return `${plan.start_time} (${days})`;
  };

  if (!property) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Propriedade não encontrada
          </h2>
          <p className="text-gray-600">
            A propriedade solicitada não foi encontrada ou você não tem
            permissão para acessá-la.
          </p>
        </div>
      </div>
    );
  }

  // Show project not found if no project is available
  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Projeto não encontrado
          </h2>
          <p className="text-gray-600">
            O projeto solicitado não foi encontrado ou você não tem permissão
            para acessá-lo.
          </p>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="p-4 pb-20 bg-gray-50 min-h-screen">
      {/* Seção Resumo do Projeto */}
      <div className="bg-gradient-to-br from-green-50 via-white to-white rounded-2xl border border-gray-100 p-5 mb-6 shadow-lg">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-800 mb-1">
              {project.name}
            </h1>
            <p className="text-gray-600 text-sm">{project.description}</p>
          </div>
          <button
            className="p-2 text-gray-500 bg-white/60 rounded-full hover:bg-gray-100 hover:text-gray-700 transition-all duration-200 shadow-sm"
            onClick={() => setLocation(`/app/projects/${projectId}/config`)}
          >
            <Settings className="h-5 w-5" />
          </button>
        </div>

        {/* Grade de Estatísticas do Projeto */}
        <div className="grid grid-cols-3 gap-4 mb-5 text-center">
          <div>
            <p className="text-2xl font-semibold text-gray-800">
              {sectors?.length || 0}
            </p>
            <p className="text-xs text-gray-500">Setores</p>
          </div>
          <div>
            <p className="text-2xl font-semibold text-gray-800">
              {getTotalArea()}
            </p>
            <p className="text-xs text-gray-500">Hectares</p>
          </div>
          <div>
            <p className="text-2xl font-semibold text-gray-800">
              {irrigationPlans?.length || 0}
            </p>
            <p className="text-xs text-gray-500">Planos</p>
          </div>
        </div>

        {/* Funcionalidades do Projeto */}
        <div className="flex items-center justify-center gap-4 bg-green-50/50 border border-green-100 rounded-lg p-2 mb-4">
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                hasIrrigation() ? "bg-blue-500" : "bg-gray-300"
              }`}
            ></div>
            <span
              className={`text-xs font-medium ${
                hasIrrigation() ? "text-blue-800" : "text-gray-500"
              }`}
            >
              Irrigação
            </span>
          </div>
          <div className="w-px h-4 bg-green-200"></div>
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                hasFertigation() ? "bg-green-600" : "bg-gray-300"
              }`}
            ></div>
            <span
              className={`text-xs font-medium ${
                hasFertigation() ? "text-green-800" : "text-gray-500"
              }`}
            >
              Fertirrigação
            </span>
          </div>
        </div>

        {/* Configurações do Sistema de Irrigação */}
        {(project.pipe_wash_time_seconds !== null ||
          property?.backwash_duration_minutes !== null ||
          property?.backwash_period_minutes !== null) && (
          <div className="bg-white/70 border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-2">
              Configurações do Sistema
            </h3>
            <div className="grid grid-cols-2 gap-2">
              {project.pipe_wash_time_seconds !== null && (
                <div className="text-center flex flex-col  bg-blue-50 rounded-lg p-2">
                  <div className="font-bold text-blue-900 grow flex justify-center items-center">
                    {formatDuration(project.pipe_wash_time_seconds)}
                  </div>
                  <div className="text-xs text-blue-600">Lavagem</div>
                </div>
              )}
              {property?.backwash_duration_minutes !== null &&
                property?.backwash_period_minutes !== null && (
                  <div className="text-center flex flex-col bg-green-50 rounded-lg p-2">
                    <div className="font-bold text-green-900 grow flex justify-center items-center">
                      {property.backwash_duration_minutes}min /{" "}
                      {property.backwash_period_minutes}min
                    </div>
                    <div className="text-xs text-green-600">Retrolavagem</div>
                  </div>
                )}
            </div>
          </div>
        )}
      </div>

      {/* Seção de Planos de Irrigação */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Agendamentos</h2>
        </div>

        {irrigationPlans?.length === 0 ? (
          <div
            className="bg-white rounded-2xl border-2 border-dashed border-gray-200 p-8 text-center hover:border-green-400 hover:bg-green-50 transition-all duration-300 cursor-pointer"
            onClick={() => {
              setLocation(`/app/projects/${projectId}/irrigation-plans/new`);
            }}
          >
            <div className="flex justify-center items-center mx-auto w-16 h-16 bg-gray-100 rounded-full mb-4">
              <Clock className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-1">
              Nenhum agendamento
            </h3>
            <p className="text-gray-500 mb-4">
              Toque aqui para criar seu primeiro plano de irrigação.
            </p>
            <div className="inline-flex items-center gap-2 text-green-600 font-medium">
              <Plus className="h-4 w-4" />
              <span>Adicionar Novo Plano</span>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {irrigationPlans?.map((plan, index) => {
              const iconColors = [
                {
                  bg: "bg-blue-100",
                  icon: "text-blue-600",
                  border: "border-blue-200",
                },
                {
                  bg: "bg-purple-100",
                  icon: "text-purple-600",
                  border: "border-purple-200",
                },
                {
                  bg: "bg-orange-100",
                  icon: "text-orange-600",
                  border: "border-orange-200",
                },
                {
                  bg: "bg-indigo-100",
                  icon: "text-indigo-600",
                  border: "border-indigo-200",
                },
                {
                  bg: "bg-red-100",
                  icon: "text-red-600",
                  border: "border-red-200",
                },
              ];
              const colorScheme = iconColors[index % iconColors.length];

              return (
                <div
                  key={plan.id}
                  className="bg-white rounded-2xl border border-gray-200 p-4 transition-all duration-300 hover:shadow-xl hover:border-green-300 cursor-pointer shadow-lg"
                  onClick={() => {
                    setLocation(
                      `/app/projects/${projectId}/irrigation-plans/${plan.id}`
                    );
                  }}
                >
                  <div className="flex items-start gap-4">
                    <div
                      className={`w-12 h-12 rounded-full flex-shrink-0 flex items-center justify-center ${colorScheme.bg}`}
                    >
                      <Clock className={`w-6 h-6 ${colorScheme.icon}`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h3 className="text-lg font-bold text-gray-800">
                          {plan.name}
                        </h3>
                        <span
                          className={`px-3 py-1 text-xs font-semibold rounded-full flex-shrink-0 ${
                            plan.is_enabled
                              ? "bg-green-100 text-green-800"
                              : "bg-gray-100 text-gray-600"
                          }`}
                        >
                          {plan.is_enabled ? "Ativo" : "Inativo"}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm text-gray-600 mb-3">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span>{plan.start_time}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <ListChecks className="h-4 w-4 text-gray-400" />
                          <span>{plan.steps?.length || 0} etapas</span>
                        </div>
                        <div className="flex items-center gap-2 col-span-2">
                          <span className="text-lg">📅</span>
                          <span>{formatDays(plan.days_of_week)}</span>
                        </div>
                      </div>

                      <div className="text-xs text-gray-500 pt-2 border-t border-gray-100">
                        Próxima execução:{" "}
                        <span className="font-medium text-gray-600">
                          {plan.is_enabled ? "Amanhã às 06:00" : "-"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Botão Flutuante de Ação */}
      <FAB
        icon={<Plus className="h-6 w-6" />}
        onClick={() => {
          setLocation(`/app/projects/${projectId}/irrigation-plans/new`);
        }}
      />
    </div>
  );
}

export default ProjectDetailPage;
