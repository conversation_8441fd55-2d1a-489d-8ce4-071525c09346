import { Tabs } from '@/components';
import { Cpu, Wrench } from 'lucide-react';

const HARDWARE_TABS = [
  { key: 'devices', label: 'Dispositivos', icon: Cpu },
  { key: 'pumps', label: 'Bombas', icon: Wrench },
] as const;

interface HardwareTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export default function HardwareTabs({ activeTab, onTabChange }: HardwareTabsProps) {
  return (
    <Tabs
      tabs={HARDWARE_TABS}
      activeTab={activeTab}
      onTabChange={onTabChange}
      className="mb-6"
    />
  );
}