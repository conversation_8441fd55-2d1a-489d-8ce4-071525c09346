import {
  WATER_PUMP_TYPE_LABELS,
  WATER_PUMP_TYPE_VALUES,
  WATER_PUMP_MODE_LABELS,
  WATER_PUMP_MODE_VALUES,
  type WaterPumpType,
  type WaterPumpMode,
} from "@/api/model/water-pump";
import type { AUTWaterPump } from "@/api/queries/account";
import { Modal, useToast } from "@/components";
import Button from "@/components/ui/Button";
import { ModalFooter } from "@/components/ui/ModalFooter";
import { devicesAtom, selectedPropertyIdAtom, waterPumpsAtom } from "@/store";
import { createWaterPumpAtom, updateWaterPumpAtom } from "@/store/crud";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect, useMemo, useState } from "react";

interface PumpDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  pump: AUTWaterPump | null;
  mode: "create" | "edit";
}

function PumpDetailModal({
  isOpen,
  onClose,
  pump,
  mode,
}: PumpDetailModalProps) {
  const createWaterPump = useSetAtom(createWaterPumpAtom);
  const updateWaterPump = useSetAtom(updateWaterPumpAtom);
  const selectedPropertyId = useAtomValue(selectedPropertyIdAtom);
  const devices = useAtomValue(devicesAtom);
  const waterPumps = useAtomValue(waterPumpsAtom);

  const { showWarning } = useToast();

  const [formData, setFormData] = useState({
    label: "",
    identifier: "",
    pump_type: "" as WaterPumpType | "",
    pump_model: "",
    water_pump_controller: "" as string,
    has_frequency_inverter: false,
    monitor_operation: false,
    flow_rate_lh: null as number | null,
    mode: "PULSE" as WaterPumpMode,
  });

  useEffect(() => {
    if (pump && mode === "edit") {
      setFormData({
        label: pump.label,
        identifier: pump.identifier,
        pump_type: pump.pump_type,
        pump_model: pump.pump_model,
        water_pump_controller: pump.water_pump_controller || "",
        has_frequency_inverter: pump.has_frequency_inverter || false,
        monitor_operation: pump.monitor_operation || false,
        flow_rate_lh: pump.flow_rate_lh || null,
        mode: pump.mode || "PULSE",
      });
    } else if (mode === "create") {
      setFormData({
        label: "",
        identifier: "",
        pump_type: "",
        pump_model: "",
        water_pump_controller: "",
        has_frequency_inverter: false,
        monitor_operation: false,
        flow_rate_lh: null,
        mode: "PULSE",
      });
    }
  }, [pump, mode]);

  // Filter devices to only show WPC-PL10 and WPC-PL50 controllers
  const availableControllers = useMemo(() => {
    return devices.filter(
      (device) => device.model === "WPC-PL10" || device.model === "WPC-PL50"
    );
  }, [devices]);

  // Get controllers that are disabled based on assignment rules
  const getDisabledControllers = useMemo(() => {
    if (!formData.pump_type) return new Set<string>();

    const currentPumpId = pump?.id;
    const disabledControllerIds = new Set<string>();

    // Get all pumps except the current one being edited
    const otherPumps = waterPumps.filter((p) => p.id !== currentPumpId);

    otherPumps.forEach((otherPump) => {
      const controllerId = otherPump.water_pump_controller; // Now always a string

      if (!controllerId) return; // Skip pumps without controllers

      // Apply filtering rules based on pump types
      if (formData.pump_type === "SERVICE") {
        // SERVICE pumps cannot use controllers assigned to any other pump
        disabledControllerIds.add(controllerId);
      } else if (otherPump.pump_type === "SERVICE") {
        // Controllers assigned to SERVICE pumps cannot be used by any other pump
        disabledControllerIds.add(controllerId);
      } else if (formData.pump_type === otherPump.pump_type) {
        // Same type pumps cannot share controllers
        disabledControllerIds.add(controllerId);
      } else if (
        (formData.pump_type === "IRRIGATION" &&
          otherPump.pump_type === "FERTIGATION") ||
        (formData.pump_type === "FERTIGATION" &&
          otherPump.pump_type === "IRRIGATION")
      ) {
        // IRRIGATION and FERTIGATION can share controllers, so don't disable
        // This is explicitly allowed by the requirements
      }
    });

    return disabledControllerIds;
  }, [formData.pump_type, waterPumps, pump?.id]);

  // Handle pump type change - reset controller when type changes
  const handlePumpTypeChange = (newPumpType: WaterPumpType | "") => {
    setFormData((prev) => ({
      ...prev,
      pump_type: newPumpType,
      water_pump_controller: "", // Reset controller when pump type changes
    }));
  };

  const handleSave = async () => {
    if (!selectedPropertyId) {
      console.error("No property selected for the pump.");
      showWarning({
        message: "Nenhuma propriedade selecionada para a bomba.",
      });
      return;
    }
    const data = {
      ...formData,
      identifier: formData.identifier.trim() || formData.label.trim(),
      property: selectedPropertyId, // Ensure property ID is set
      pump_type: formData.pump_type || undefined, // Convert empty string to undefined
      water_pump_controller: formData.water_pump_controller || null, // Convert empty string to null
    };
    // TODO: Implement save logic with API call
    console.log("Saving pump:", data);
    if (mode === "edit" && pump) {
      await updateWaterPump({
        id: pump.id,
        data: data as any, // Cast to avoid type mismatch with DirectusRelationFieldType
      });
    } else if (mode === "create") {
      // Create new water pump
      await createWaterPump(data as any); // Cast to avoid type mismatch with DirectusRelationFieldType
    }

    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === "create" ? "Nova Bomba" : "Editar Bomba"}
      size="md"
    >
      <div className="space-y-6">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nome
          </label>
          <input
            type="text"
            value={formData.label}
            onChange={(e) =>
              setFormData({ ...formData, label: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            placeholder="Nome da Bomba"
          />
        </div>

        {/* Water Pump Serial Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            S/N
          </label>
          <input
            type="text"
            value={formData.identifier}
            onChange={(e) =>
              setFormData({ ...formData, identifier: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            placeholder="S/N XXXXXXX"
          />
        </div>

        {/* Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tipo
          </label>
          <select
            value={formData.pump_type}
            onChange={(e) =>
              handlePumpTypeChange(e.target.value as WaterPumpType | "")
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
          >
            <option value="">Selecione o tipo da bomba</option>
            {WATER_PUMP_TYPE_VALUES.map((type) => (
              <option key={type} value={type}>
                {WATER_PUMP_TYPE_LABELS[type]}
              </option>
            ))}
          </select>
        </div>

        {/* Water Pump Controller */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Controlador
          </label>
          <select
            value={formData.water_pump_controller}
            onChange={(e) =>
              setFormData({
                ...formData,
                water_pump_controller: e.target.value,
              })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            disabled={!formData.pump_type}
          >
            <option value="">
              {!formData.pump_type
                ? "Selecione primeiro o tipo da bomba"
                : "Selecione o controlador"}
            </option>
            {availableControllers.map((controller) => {
              const isDisabled = getDisabledControllers.has(controller.id);

              // Find which pump is using this controller to provide better context
              let disabledReason = "";
              if (isDisabled) {
                const usingPump = waterPumps.find((p) => {
                  const controllerId = p.water_pump_controller; // Now always a string
                  return controllerId === controller.id && p.id !== pump?.id;
                });

                if (usingPump) {
                  const pumpTypeLabel =
                    WATER_PUMP_TYPE_LABELS[usingPump.pump_type];
                  disabledReason = ` (Usado por bomba ${pumpTypeLabel})`;
                } else {
                  disabledReason = " (Em uso)";
                }
              }

              return (
                <option
                  key={controller.id}
                  value={controller.id}
                  disabled={isDisabled}
                >
                  {controller.identifier} - {controller.model}
                  {disabledReason}
                </option>
              );
            })}
          </select>
        </div>

        {/* Model */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Modelo
          </label>
          <input
            type="text"
            value={formData.pump_model}
            onChange={(e) =>
              setFormData({ ...formData, pump_model: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            placeholder="Modelo da bomba"
          />
        </div>

        {/* Mode */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Modo
          </label>
          <div className="flex gap-4">
            {WATER_PUMP_MODE_VALUES.map((modeValue) => (
              <label key={modeValue} className="flex items-center gap-1">
                <input
                  type="radio"
                  name="pump-mode"
                  value={modeValue}
                  checked={formData.mode === modeValue}
                  onChange={() =>
                    setFormData({
                      ...formData,
                      mode: modeValue as WaterPumpMode,
                    })
                  }
                  className="text-green-600 focus:ring-green-500"
                />
                <span className="text-sm">
                  {WATER_PUMP_MODE_LABELS[modeValue]}
                </span>
              </label>
            ))}
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Modo de operação da bomba
          </p>
        </div>

        {/* Frequency Inverter */}
        <div>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={formData.has_frequency_inverter}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  has_frequency_inverter: e.target.checked,
                })
              }
              className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
            />
            <span className="text-sm font-medium text-gray-700">
              Possui inversor de frequência
            </span>
          </label>
        </div>

        {/* Monitor Operation: Only show if pump_type is not 'FERTIGATION' */}
        {formData.pump_type !== "FERTIGATION" && (
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={formData.monitor_operation}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    monitor_operation: e.target.checked,
                  })
                }
                className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
              />
              <span className="text-sm font-medium text-gray-700">
                Monitorar operação da bomba
              </span>
            </label>
          </div>
        )}

        {/* Flow Rate */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Vazão (L/h)
          </label>
          <input
            type="number"
            value={formData.flow_rate_lh || ""}
            onChange={(e) =>
              setFormData({
                ...formData,
                flow_rate_lh: e.target.value
                  ? parseFloat(e.target.value)
                  : null,
              })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            placeholder="1000"
            min="0"
            step="0.1"
          />
          <p className="mt-1 text-xs text-gray-500">
            Taxa de fluxo da bomba em litros por hora
          </p>
        </div>

        {/* Action Buttons */}
        <ModalFooter
          primaryAction={{
            label: "Salvar",
            onClick: handleSave,
          }}
          secondaryAction={{
            label: "Cancelar",
            onClick: handleCancel,
          }}
        />
      </div>
    </Modal>
  );
}

export default PumpDetailModal;
