import { useAtomValue } from 'jotai';
import { waterPumpsAtom, propertyDevicesAtom, selectedPropertyAtom } from '@/store';
import type { AUTWaterPump } from '@/api/queries/account';
import PumpList from './PumpList';

interface PumpsTabProps {
  searchQuery: string;
  onPumpClick: (pump: AUTWaterPump) => void;
}

export default function PumpsTab({ searchQuery, onPumpClick }: PumpsTabProps) {
  const currentPumps = useAtomValue(waterPumpsAtom);
  const propertyDevices = useAtomValue(propertyDevicesAtom);
  const selectedProperty = useAtomValue(selectedPropertyAtom);

  // Filter pumps based on search query
  const filteredPumps = currentPumps.filter(
    (pump) =>
      pump.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pump.identifier.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pump.pump_model.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <PumpList
      pumps={filteredPumps}
      propertyDevices={propertyDevices}
      selectedProperty={selectedProperty}
      onPumpClick={onPumpClick}
      hasSearchQuery={!!searchQuery.trim()}
    />
  );
}