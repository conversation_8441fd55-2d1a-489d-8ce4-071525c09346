import type { DeviceWithMapping } from '@/utils/mesh-device-utils';
import type { AUTProperty } from '@/api/queries/account';
import EnhancedDeviceListItem from './EnhancedDeviceListItem';
import EmptyDeviceState from './EmptyDeviceState';

interface DeviceListProps {
  devices: DeviceWithMapping[];
  property: AUTProperty | null;
  onDeviceClick: (device: DeviceWithMapping) => void;
  onManageNetwork: (device: DeviceWithMapping) => void;
  hasSearchQuery: boolean;
  hasFilter: boolean;
}

export default function DeviceList({
  devices,
  property,
  onDeviceClick,
  onManageNetwork,
  hasSearchQuery,
  hasFilter,
}: DeviceListProps) {
  if (devices.length === 0) {
    return <EmptyDeviceState hasSearchQuery={hasSearchQuery} hasFilter={hasFilter} />;
  }

  return (
    <div className="space-y-3">
      {devices.map((device, index) => (
        <EnhancedDeviceListItem
          key={device.id}
          device={device}
          property={property}
          index={index}
          devices={devices}
          onDeviceClick={onDeviceClick}
          onManageNetwork={onManageNetwork}
        />
      ))}
    </div>
  );
}