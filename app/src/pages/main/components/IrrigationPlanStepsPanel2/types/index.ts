import { AUTSector } from "@/api/queries/account";
import {
  IrrigationPlanFormData,
  IrrigationPlanStepFormData,
} from "@/pages/main/IrrigationPlanWizardPage";

/**
 * Represents a sector that can be selected for irrigation plan steps.
 */
export type Sector = Pick<AUTSector, "id" | "name" | "description" | "power">;

/**
 * Props for the IrrigationPlanStepsPanel2 component.
 */
export interface IrrigationPlanStepsPanel2Props {
  /** ID of the project containing the sectors */
  projectId: string;

  /** Irrigation plan data containing fertigation settings */
  planData: IrrigationPlanFormData;

  /** Array of irrigation plan steps to display and manage */
  steps: IrrigationPlanStepFormData[];

  /** Available sectors that can be assigned to steps */
  sectors: Sector[];

  /** Project data containing pipe wash time and other settings */
  projectData?: {
    pipe_wash_time_seconds: number | null;
  };

  /** Whether the project's irrigation pump has frequency inverter */
  hasFrequencyInverter?: boolean;

  /** Whether the component is in read-only mode */
  readOnly?: boolean;

  /** Whether the component is in loading state */
  loading?: boolean;

  /**
   * Callback triggered when user wants to add a new step.
   */
  onAddStep: () => void;

  /**
   * Callback triggered when user confirms removal of a step.
   */
  onRemoveStep: (stepId: string) => Promise<void>;

  /**
   * Callback triggered when user clicks save/confirm button for a step.
   */
  onSaveStep: (step: IrrigationPlanStepFormData) => Promise<void>;

  /**
   * Callback triggered for real-time local updates as user modifies step fields.
   */
  onUpdateStep: (
    stepId: string,
    updates: Partial<IrrigationPlanStepFormData>
  ) => void;

  /**
   * Callback triggered when user reorders steps using up/down arrows.
   */
  onMoveStep: (stepId: string, direction: "up" | "down") => void;

  /**
   * Optional callback triggered when multiple steps need to be removed at once.
   * If not provided, onRemoveStep will be called for each step individually.
   */
  onBulkRemoveSteps?: (stepIds: string[]) => Promise<void>;

  /**
   * Callback triggered when user reorders steps using the reorder assistant.
   */
  onReorderSteps?: (newOrder: IrrigationPlanStepFormData[]) => void;
}

/**
 * Props for the SummarySection component.
 */
export interface SummarySectionProps {
  steps: IrrigationPlanStepFormData[];
  sectors: Sector[];
  planData: IrrigationPlanFormData;
}

/**
 * Props for the StepCard component.
 */
export interface StepCardProps {
  step: IrrigationPlanStepFormData;
  index: number;
  totalSteps: number;
  isSelected: boolean;
  fertigationEnabled: boolean;
  readOnly: boolean;
  sectors: Sector[];
  hasFrequencyInverter?: boolean;
  onToggleSelect: (stepId: string) => void;
  onMoveUp: (stepId: string) => void;
  onMoveDown: (stepId: string) => void;
  onEdit: (stepId: string) => void;
  onDelete: (stepId: string) => void;
}

/**
 * Props for the StepList component.
 */
export interface StepListProps {
  steps: IrrigationPlanStepFormData[];
  selectedSteps: Set<string>;
  fertigationEnabled: boolean;
  readOnly: boolean;
  sectors: Sector[];
  hasFrequencyInverter?: boolean;
  onToggleStepSelect: (stepId: string) => void;
  onSelectAll: (stepIds: string[]) => void;
  onClearSelection: () => void;
  onMoveStep: (stepId: string, direction: "up" | "down") => void;
  onEditStep: (stepId: string) => void;
  onDeleteStep: (stepId: string) => void;
}

/**
 * Props for the BulkActionBar component.
 */
export interface BulkActionBarProps {
  selectedCount: number;
  readOnly: boolean;
  onBulkEdit: () => void;
}

/**
 * Props for the ChooseSectorsDialog component.
 */
export interface ChooseSectorsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  sectors: Sector[];
  selectedSectorIds: string[];
  onSectorsChange: (sectorIds: string[]) => Promise<void>;
}

/**
 * Props for the StepEditDialog component.
 */
export interface StepEditDialogProps {
  isOpen: boolean;
  onClose: () => void;
  step: IrrigationPlanStepFormData | null;
  fertigationEnabled: boolean;
  projectData?: {
    pipe_wash_time_seconds: number | null;
  };
  onSave: (step: IrrigationPlanStepFormData) => void;
}

/**
 * Props for the StepEditManyDialog component.
 */
export interface StepEditManyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSteps: IrrigationPlanStepFormData[];
  fertigationEnabled: boolean;
  projectData?: {
    pipe_wash_time_seconds: number | null;
  };
  onSave: (updates: Partial<IrrigationPlanStepFormData>) => void;
}

/**
 * Props for the StepReorderAssistant component.
 */
export interface StepReorderAssistantProps {
  isOpen: boolean;
  onClose: () => void;
  steps: IrrigationPlanStepFormData[];
  onReorderSteps: (newOrder: IrrigationPlanStepFormData[]) => void;
}
