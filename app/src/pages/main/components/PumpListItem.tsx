import { WATER_PUMP_TYPE_LABELS } from '@/api/model/water-pump';
import type { AUTWaterPump, AUTPropertyDevice, AUTProperty } from '@/api/queries/account';
import { resolveDeviceDisplayInfo, formatDeviceDisplayText, formatLICDisplayText } from '@/utils/device-label-utils';
import { findWaterPumpAssociations, formatDeviceAssociations } from '@/utils/device-associations';
import { ChevronRight, Wrench } from 'lucide-react';

interface PumpListItemProps {
  pump: AUTWaterPump;
  propertyDevices: AUTPropertyDevice[];
  selectedProperty: AUTProperty | null;
  onClick: (pump: AUTWaterPump) => void;
}

function getPumpLastActivity(_pump: AUTWaterPump) {
  // This would come from the API with real activity data
  return 'Agora';
}

interface InfoCardProps {
  title: string;
  value: string;
  isHighlight?: boolean;
  icon?: React.ReactNode;
}

function InfoCard({ title, value, isHighlight = false, icon }: InfoCardProps) {
  return (
    <div className={`p-3 rounded-lg border ${isHighlight ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'}`}>
      <div className="flex items-center gap-1 mb-1">
        {icon}
        <span className={`text-xs font-medium ${isHighlight ? 'text-blue-700' : 'text-gray-600'}`}>
          {title}
        </span>
      </div>
      <p className={`text-sm font-medium ${isHighlight ? 'text-blue-900' : 'text-gray-900'}`}>
        {value}
      </p>
    </div>
  );
}

export default function PumpListItem({ pump, propertyDevices, selectedProperty, onClick }: PumpListItemProps) {
  // Resolve pump controller display information
  const pumpControllerInfo = resolveDeviceDisplayInfo(
    pump.water_pump_controller,
    propertyDevices
  );

  // Get water pump associations
  const associations = findWaterPumpAssociations(pump.id, selectedProperty);
  const associationText = formatDeviceAssociations(associations);

  return (
    <div
      onClick={() => onClick(pump)}
      className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors"
    >
      {/* Header with pump name and icon */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Wrench className="text-gray-400" size={16} />
          <span className="font-medium text-gray-900">
            {pump.label}
          </span>
        </div>
        <ChevronRight className="text-gray-400" size={20} />
      </div>

      {/* Grid of info cards */}
      <div className="grid grid-cols-2 lg:grid-cols-3 gap-3">
        <InfoCard
          title="S/N"
          value={pump.identifier}
        />
        
        <InfoCard
          title="Tipo"
          value={WATER_PUMP_TYPE_LABELS[pump.pump_type] || pump.pump_type}
        />
        
        <InfoCard
          title="Controlador"
          value={formatDeviceDisplayText(pumpControllerInfo, 'Não definido')}
        />
        
        {pumpControllerInfo?.licInfo && (
          <InfoCard
            title="LIC"
            value={formatLICDisplayText(pumpControllerInfo.licInfo) || 'Não definido'}
          />
        )}
        
        <InfoCard
          title="Inversor de frequência"
          value={pump.has_frequency_inverter ? 'Sim' : 'Não'}
        />
        
        <InfoCard
          title="Monitoramento"
          value={pump.monitor_operation ? 'Ativo' : 'Inativo'}
        />
        
        <InfoCard
          title="Última atividade"
          value={getPumpLastActivity(pump)}
        />
        
        {associationText && (
          <InfoCard
            title="Associações"
            value={associationText}
            isHighlight={true}
            icon={<span className="text-blue-600">🔗</span>}
          />
        )}
      </div>
    </div>
  );
}