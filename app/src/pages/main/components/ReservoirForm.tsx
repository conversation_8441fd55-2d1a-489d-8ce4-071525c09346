import type { Reservoir } from "@/api/model/reservoir";
import {
  createReser<PERSON>ir<PERSON>tom,
  propertyDevicesAtom,
  reservoirsAtom,
  reservoirsByIdAtom,
  selectedPropertyIdAtom,
  updateReservoirAtom,
  waterPumpsAtom,
} from "@/store";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect, useMemo, useState } from "react";
import { useLocation, useParams } from "wouter";
import { ModalFooter } from "@/components/ui/ModalFooter";

interface ReservoirFormData {
  name: string;
  description: string;
  capacity: string;
  safety_time_minutes: string;
  reservoir_monitor: string;
  water_pump: string;
  enabled: boolean;
  notes: string;
}

export function ReservoirForm() {
  const [, setLocation] = useLocation();
  const { id } = useParams<{ id: string }>();
  const isEditing = id !== "new";

  const propertyDevices = useAtomValue(propertyDevicesAtom);
  const waterPumps = useAtomValue(waterPumpsAtom);
  const selectedPropertyId = useAtomValue(selectedPropertyIdAtom);
  const reservoirById = useAtomValue(reservoirsByIdAtom);
  const reservoirs = useAtomValue(reservoirsAtom);
  // IDs of RM devices already assigned to other reservoirs (excluding current when editing)
  const usedRMDeviceIds = reservoirs
    .filter((r: any) => !isEditing || r.id !== id)
    .map((r: any) =>
      r.reservoir_monitor && typeof r.reservoir_monitor === "object"
        ? r.reservoir_monitor.id
        : r.reservoir_monitor
    )
    .filter(Boolean) as string[];
  // IDs of water pumps already assigned to other reservoirs (excluding current when editing)
  const usedPumpIds = reservoirs
    .filter((r: any) => !isEditing || r.id !== id)
    .map((r: any) =>
      r.water_pump && typeof r.water_pump === "object"
        ? r.water_pump.id
        : r.water_pump
    )
    .filter(Boolean) as string[];
  const createReservoir = useSetAtom(createReservoirAtom);
  const updateReservoir = useSetAtom(updateReservoirAtom);

  const [formData, setFormData] = useState<ReservoirFormData>({
    name: "",
    description: "",
    capacity: "",
    safety_time_minutes: "",
    reservoir_monitor: "",
    water_pump: "",
    enabled: true,
    notes: "",
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create a map from a device ID to its LIC's property_device ID
  const deviceToLicMap = useMemo(() => {
    const map = new Map<string, string | null>();
    propertyDevices.forEach((pd) => {
      if (pd.device?.id) {
        const lic = pd.current_mesh_device_mapping?.lic_property_device;
        if (typeof lic === "object" && lic !== null && "id" in lic) {
          map.set(pd.device.id, (lic as { id: string }).id);
        } else if (typeof lic === "string") {
          map.set(pd.device.id, lic);
        } else {
          map.set(pd.device.id, null);
        }
      }
    });
    return map;
  }, [propertyDevices]);

  // Create a map from a water pump ID to its LIC's property_device ID
  const pumpToLicMap = useMemo(() => {
    const map = new Map<string, string | null>();
    waterPumps.forEach((pump) => {
      const controller = pump.water_pump_controller;
      let controllerId: string | null = null;
      if (typeof controller === "object" && controller !== null) {
        controllerId = (controller as any).id;
      } else if (typeof controller === "string") {
        controllerId = controller;
      }

      if (pump.id && controllerId) {
        map.set(pump.id, deviceToLicMap.get(controllerId) ?? null);
      }
    });
    return map;
  }, [waterPumps, deviceToLicMap]);

  // Load existing reservoir data when editing or reset when creating
  useEffect(() => {
    if (isEditing && id) {
      const reservoir = reservoirById(id);
      if (reservoir) {
        setFormData({
          name: (reservoir as any).name || "",
          description: (reservoir as any).description || "",
          capacity: (reservoir as any).capacity?.toString() || "",
          safety_time_minutes: (reservoir as any).safety_time_minutes?.toString() || "",
          reservoir_monitor: (reservoir as any).reservoir_monitor || "",
          water_pump: (reservoir as any).water_pump || "",
          enabled: (reservoir as any).enabled,
          notes: (reservoir as any).notes || "",
        });
      }
    } else if (!isEditing) {
      // Reset form data when creating new reservoir
      setFormData({
        name: "",
        description: "",
        capacity: "",
        safety_time_minutes: "",
        reservoir_monitor: "",
        water_pump: "",
        enabled: true,
        notes: "",
      });
    }
  }, [isEditing, id, reservoirById]);

  // Filter devices to only show RM (Reservoir Monitor) devices
  const rmPropertyDevices = propertyDevices.filter(
    (pd) => pd.device?.model === "RM"
  );

  // Filter water pumps to only show SERVICE type pumps
  const servicePumps = waterPumps.filter(
    (pump: any) => pump.pump_type === "SERVICE"
  );

  // Get the LIC ID for the currently selected devices
  const selectedRmLicId = formData.reservoir_monitor
    ? deviceToLicMap.get(formData.reservoir_monitor)
    : undefined;
  const selectedPumpLicId = formData.water_pump
    ? pumpToLicMap.get(formData.water_pump)
    : undefined;

  // Filter the dropdown options based on the selection in the other dropdown
  const filteredRmDevices = useMemo(() => {
    if (selectedPumpLicId === undefined || selectedPumpLicId === null) {
      return rmPropertyDevices; // No pump selected or pump has no LIC, show all
    }
    return rmPropertyDevices.filter((pd) => {
      const licId = deviceToLicMap.get(pd.device.id);
      // Show if it has the same LIC or if it's the currently selected one
      return (
        licId === selectedPumpLicId ||
        pd.device.id === formData.reservoir_monitor
      );
    });
  }, [
    rmPropertyDevices,
    selectedPumpLicId,
    deviceToLicMap,
    formData.reservoir_monitor,
  ]);

  const filteredServicePumps = useMemo(() => {
    if (selectedRmLicId === undefined || selectedRmLicId === null) {
      return servicePumps; // No RM selected or RM has no LIC, show all
    }
    return servicePumps.filter((pump) => {
      const licId = pumpToLicMap.get(pump.id);
      // Show if it has the same LIC or if it's the currently selected one
      return licId === selectedRmLicId || pump.id === formData.water_pump;
    });
  }, [servicePumps, selectedRmLicId, pumpToLicMap, formData.water_pump]);

  const handleInputChange =
    (field: keyof ReservoirFormData) =>
    (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: event.target.value,
      }));
    };

  const handleSelectChange =
    (field: keyof ReservoirFormData) =>
    (event: React.ChangeEvent<HTMLSelectElement>) => {
      const { value } = event.target;

      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));

      // When RM is changed, check if the pump is still valid
      if (field === "reservoir_monitor") {
        if (value && formData.water_pump) {
          const newRmLicId = deviceToLicMap.get(value);
          const currentPumpLicId = pumpToLicMap.get(formData.water_pump);
          if (
            newRmLicId !== undefined &&
            currentPumpLicId !== undefined &&
            newRmLicId !== currentPumpLicId
          ) {
            setFormData((prev) => ({ ...prev, water_pump: "" }));
          }
        }
      }

      // When pump is changed, check if the RM is still valid
      if (field === "water_pump") {
        if (value && formData.reservoir_monitor) {
          const newPumpLicId = pumpToLicMap.get(value);
          const currentRmLicId = deviceToLicMap.get(formData.reservoir_monitor);
          if (
            newPumpLicId !== undefined &&
            currentRmLicId !== undefined &&
            newPumpLicId !== currentRmLicId
          ) {
            setFormData((prev) => ({ ...prev, reservoir_monitor: "" }));
          }
        }
      }
    };

  const handleCheckboxChange =
    (field: keyof ReservoirFormData) =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: event.target.checked,
      }));
    };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setLoading(true);
    setError(null);

    try {
      const reservoirData: Partial<Reservoir> = {
        property: selectedPropertyId!,
        name: formData.name,
        description: formData.description || null,
        capacity: formData.capacity ? parseFloat(formData.capacity) : null,
        safety_time_minutes: formData.safety_time_minutes ? parseInt(formData.safety_time_minutes) : null,
        reservoir_monitor: formData.reservoir_monitor || null,
        water_pump: formData.water_pump || null,
        enabled: formData.enabled,
        notes: formData.notes || null,
      };

      if (isEditing && id) {
        await updateReservoir({ id, data: reservoirData });
      } else {
        await createReservoir(reservoirData);
      }

      setLocation("/app/reservoirs");
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Erro ao salvar reservatório"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setLocation("/app/reservoirs");
  };

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">
        {isEditing ? "Editar Reservatório" : "Novo Reservatório"}
      </h1>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label
            htmlFor="name"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Nome *
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={handleInputChange("name")}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Nome do reservatório"
          />
        </div>

        <div>
          <label
            htmlFor="description"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Descrição
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={handleInputChange("description")}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Descrição do reservatório"
          />
        </div>

        <div>
          <label
            htmlFor="capacity"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Capacidade (litros)
          </label>
          <input
            type="number"
            id="capacity"
            value={formData.capacity}
            onChange={handleInputChange("capacity")}
            min="0"
            step="0.1"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Capacidade em litros"
          />
        </div>

        <div>
          <label
            htmlFor="safety_time_minutes"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Tempo de Segurança (minutos)
          </label>
          <input
            type="number"
            id="safety_time_minutes"
            value={formData.safety_time_minutes}
            onChange={handleInputChange("safety_time_minutes")}
            min="1"
            step="1"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Tempo de segurança em minutos"
          />
          <p className="text-xs text-gray-500 mt-1">
            Tempo máximo que a bomba funcionará para reabastecer o reservatório antes de parar automaticamente por segurança
          </p>
        </div>

        <div>
          <label
            htmlFor="reservoir_monitor"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Monitor de Reservatório (RM)
          </label>
          <select
            id="reservoir_monitor"
            value={formData.reservoir_monitor}
            onChange={handleSelectChange("reservoir_monitor")}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          >
            <option value="">Nenhum</option>
            {filteredRmDevices.map((pd: any) => (
              <option
                key={pd.device.id}
                value={pd.device.id}
                disabled={
                  usedRMDeviceIds.includes(pd.device.id) &&
                  pd.device.id !== formData.reservoir_monitor
                }
              >
                {pd.device.identifier} - {pd.device.model}
              </option>
            ))}
          </select>
          {formData.reservoir_monitor &&
            deviceToLicMap.get(formData.reservoir_monitor) === null && (
              <p className="text-xs text-yellow-600 mt-1">
                Este monitor não está em uma rede mesh.
              </p>
            )}
        </div>

        <div>
          <label
            htmlFor="water_pump"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Bomba de Serviço
          </label>
          <select
            id="water_pump"
            value={formData.water_pump}
            onChange={handleSelectChange("water_pump")}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          >
            <option value="">Nenhuma</option>
            {filteredServicePumps.map((pump: any) => (
              <option
                key={pump.id}
                value={pump.id}
                disabled={
                  usedPumpIds.includes(pump.id) &&
                  pump.id !== formData.water_pump
                }
              >
                {pump.label} - {pump.identifier}
              </option>
            ))}
          </select>
          {formData.water_pump &&
            pumpToLicMap.get(formData.water_pump) === null && (
              <p className="text-xs text-yellow-600 mt-1">
                A bomba deste dispositivo não está em uma rede mesh.
              </p>
            )}
        </div>

        <div>
          <label
            htmlFor="notes"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Observações
          </label>
          <textarea
            id="notes"
            value={formData.notes}
            onChange={handleInputChange("notes")}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Observações adicionais"
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="enabled"
            checked={formData.enabled}
            onChange={handleCheckboxChange("enabled")}
            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
          />
          <label htmlFor="enabled" className="ml-2 block text-sm text-gray-900">
            Ativo
          </label>
        </div>

        <ModalFooter
          primaryAction={{
            label: loading ? "Salvando..." : isEditing ? "Atualizar" : "Criar",
            onClick: () => {},
            disabled: loading || !formData.name.trim(),
            loading: loading,
            buttonType: "submit",
          }}
          secondaryAction={{
            label: "Cancelar",
            onClick: handleCancel,
            buttonType: "button",
          }}
        />
      </form>
    </div>
  );
}
