import { Tabs } from "@/components";
import {
  createIrrigation<PERSON>lan<PERSON>tom,
  createIrrigationPlanStep<PERSON>tom,
  deleteIrrigationPlanStep<PERSON>tom,
  hasActiveLoading<PERSON>tom,
  refetchDataAtom,
  swapIrrigationPlanStepsOrdersAtom,
  updateIrrigationPlanAtom,
  updateIrrigationPlanStepAtom,
} from "@/store";
import {
  irrigationPlanByIdAtom,
  projectByIdAtom,
  propertiesAtom,
  sectorsByProjectIdAtom,
} from "@/store/data";
import { useAtomValue, useSetAtom } from "jotai";
import { Map, Settings } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { useLocation, useSearchParams } from "wouter";
import Button from "@/components/ui/Button";
import ConfirmModal from "../../components/ConfirmModal";
import IrrigationPlanConfigPanel from "./components/IrrigationPlanConfigPanel";
import IrrigationPlanStepsPanel2 from "./components/IrrigationPlanStepsPanel2";
import {
  IrrigationPlanFormData,
  IrrigationPlanStepFormData,
} from "./IrrigationPlanWizardPage";
import { updateIrrigationPlanStepBatchAtom } from "@/store/crud";

// Simple validation helper to determine if plan data is valid
function isPlanDataValid(planData: IrrigationPlanFormData): boolean {
  const nameOk = planData.name.trim().length > 0;
  const startTimeOk =
    !!planData.startTime && planData.startTime.trim().length > 0;
  const daysOk =
    Array.isArray(planData.daysOfWeek) && planData.daysOfWeek.length > 0;

  // Date rules:
  // - both empty => OK (no validity period)
  // - only one filled => invalid
  // - both filled => startDate <= endDate
  const hasStart = !!planData.startDate && planData.startDate.trim().length > 0;
  const hasEnd = !!planData.endDate && planData.endDate.trim().length > 0;

  let datesOk = true;
  if (hasStart !== hasEnd) {
    datesOk = false;
  } else if (hasStart && hasEnd) {
    // Compare as YYYY-MM-DD strings which are lexicographically comparable
    datesOk = planData.startDate! <= planData.endDate!;
  }

  return nameOk && startTimeOk && daysOk && datesOk;
}

// Define tabs configuration with full type safety
const IRRIGATION_PLAN_TABS = [
  { key: "config", label: "Config.", icon: Settings },
  { key: "sectors", label: "Setores", icon: Map },
] as const;

type TabType = (typeof IRRIGATION_PLAN_TABS)[number]["key"];

type IrrigationPlanDetailPageProps = {
  planId: string;
  activeTab: string;
};

function IrrigationPlanPage({
  planId,
  activeTab,
}: IrrigationPlanDetailPageProps) {
  if (activeTab && !IRRIGATION_PLAN_TABS.some((tab) => tab.key === activeTab)) {
    activeTab = "config"; // Default to config if invalid
  }
  const [, setLocation] = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();

  // Determine if this is create mode
  const isCreateMode = planId === "new";

  // Store atoms
  const irrigationPlanById = useAtomValue(irrigationPlanByIdAtom);
  const projectById = useAtomValue(projectByIdAtom);
  const sectorsByProjectId = useAtomValue(sectorsByProjectIdAtom);
  const properties = useAtomValue(propertiesAtom);

  const createPlan = useSetAtom(createIrrigationPlanAtom);
  const updatePlan = useSetAtom(updateIrrigationPlanAtom);
  const refetchData = useSetAtom(refetchDataAtom);
  const loading = useAtomValue(hasActiveLoadingAtom);

  // CRUD operations
  const createPlanStep = useSetAtom(createIrrigationPlanStepAtom);
  const updatePlanStep = useSetAtom(updateIrrigationPlanStepAtom);
  const deletePlanStep = useSetAtom(deleteIrrigationPlanStepAtom);
  const swapPlanStepsOrders = useSetAtom(swapIrrigationPlanStepsOrdersAtom);
  const updatePlanStepBatch = useSetAtom(updateIrrigationPlanStepBatchAtom);

  // Get plan and project data
  const plan = useMemo(() => {
    return isCreateMode ? null : irrigationPlanById(planId);
  }, [irrigationPlanById, planId, isCreateMode]);

  // In create mode, get projectId from search params; in edit mode, get from plan
  const projectId = useMemo(() => {
    if (isCreateMode) {
      return searchParams.get("projectId");
    }
    return plan?.project as string | undefined;
  }, [isCreateMode, searchParams, plan]);

  const project = useMemo(() => {
    return projectId ? projectById(projectId) : null;
  }, [projectById, projectId]);

  const sectors = projectId ? sectorsByProjectId(projectId) ?? [] : [];
  
  // Check if the project's irrigation pump has frequency inverter
  const hasFrequencyInverter = useMemo(() => {
    if (!project || !project.irrigation_water_pump) return false;
    
    const irrigationPump = properties
      .flatMap((p) => p.water_pumps)
      .find((pump) => pump.id === project.irrigation_water_pump);
    
    return irrigationPump?.has_frequency_inverter || false;
  }, [project, properties]);

  // Local state for form data
  const [planSteps, setPlanSteps] = useState<IrrigationPlanStepFormData[]>([]);
  const [planData, setPlanData] = useState<IrrigationPlanFormData>({
    name: "",
    description: "",
    startTime: "06:00",
    daysOfWeek: [],
    isEnabled: true,
    fertigationEnabled: false,
    backwashEnabled: false,
    startDate: "",
    endDate: "",
  });
  const [hasChanges, setHasChanges] = useState(isCreateMode); // In create mode, we always have changes initially
  const [showDiscardModal, setShowDiscardModal] = useState(false);

  // Load plan data when plan changes (skip in create mode)
  useEffect(() => {
    if (!isCreateMode && plan) {
      // Convert API model to form data
      setPlanData({
        name: plan.name,
        description: plan.description || "",
        startTime: plan.start_time,
        daysOfWeek: plan.days_of_week,
        isEnabled: plan.is_enabled,
        fertigationEnabled: plan.fertigation_enabled,
        backwashEnabled: plan.backwash_enabled,
        startDate: plan.start_date || "",
        endDate: plan.end_date || "",
      });

      // Convert plan steps to form data
      const formSteps: IrrigationPlanStepFormData[] = (plan.steps || []).map(
        (step) => {
          const sectorId = step.sector; // Now always a string
          const sector = sectors.find((s) => s.id === sectorId);
          const sectorName = sector ? sector.name : "Setor não encontrado";

          return {
            id: step.id,
            sectorId,
            sectorName,
            order: step.order,
            durationMinutes: Math.round(step.duration_seconds / 60),
            fertigationStartDelayMinutes: step.fertigation_start_delay_seconds
              ? Math.round(step.fertigation_start_delay_seconds / 60)
              : undefined,
            fertigationDurationMinutes: step.fertigation_duration_seconds
              ? Math.round(step.fertigation_duration_seconds / 60)
              : undefined,
          };
        }
      );
      setPlanSteps(formSteps);
      setHasChanges(false); // Reset changes flag when loading existing data
    }
  }, [plan, isCreateMode, sectors]);

  const setActiveTab = (tab: TabType) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set("activeTab", tab);
      return newParams;
    });
  };

  const handlePlanDataChange = (updates: Partial<IrrigationPlanFormData>) => {
    setPlanData((prev) => ({ ...prev, ...updates }));
    setHasChanges(true);
  };

  // Step management functions
  const handleAddStep = () => {
    const availableSectors = sectors.filter(
      (sector) => !planSteps.some((step) => step.sectorId === sector.id)
    );

    if (availableSectors.length === 0) {
      return; // No more sectors available
    }

    const newStep: IrrigationPlanStepFormData = {
      id: `step-${Date.now()}`,
      sectorId: availableSectors[0].id,
      sectorName: availableSectors[0].name,
      order: planSteps.length + 1,
      durationMinutes: 20, // 20 minutes default
      fertigationStartDelayMinutes: undefined,
      fertigationDurationMinutes: undefined,
    };

    setPlanSteps((prev) => [...prev, newStep]);
    setHasChanges(true);
  };

  const handleRemoveStep = async (stepId: string) => {
    await handleBulkRemoveSteps([stepId]);
  };

  const handleBulkRemoveSteps = async (stepIds: string[]) => {
    try {
      // Separate new steps from existing steps
      const existingStepIds = stepIds.filter((id) => !id.startsWith("step-"));

      // Delete existing steps from API
      for (const stepId of existingStepIds) {
        const result = await deletePlanStep(stepId);
        if (!result.success) {
          console.error("Failed to delete step:", result.error);
          return;
        }
      }

      // Calculate updated steps with new order after removing all specified steps
      const remainingSteps = planSteps.filter(
        (step) => !stepIds.includes(step.id)
      );
      const updatedSteps = remainingSteps.map((step, index) => ({
        ...step,
        order: index + 1,
      }));

      // Update local state
      setPlanSteps(updatedSteps);
      setHasChanges(true);

      // Persist order changes for existing steps that had their order changed
      const stepsWithChangedOrder = updatedSteps.filter((updatedStep) => {
        const originalStep = remainingSteps.find(
          (s) => s.id === updatedStep.id
        );
        return (
          originalStep &&
          originalStep.order !== updatedStep.order &&
          !updatedStep.id.startsWith("step-") // Only persist existing steps
        );
      });

      // Update order for existing steps that had their order changed
      for (const step of stepsWithChangedOrder) {
        const stepData = {
          order: step.order,
        };

        const result = await updatePlanStep({ id: step.id, data: stepData });
        if (!result.success) {
          console.error("Failed to update step order:", result.error);
        }
      }

      // Data is already refetched by the CRUD operations
      // No need to call refetchData again
    } catch (error) {
      console.error("Error removing steps:", error);
    }
  };

  const handleSaveStep = async (step: IrrigationPlanStepFormData) => {
    try {
      const isNewStep = step.id.startsWith("step-");

      if (isNewStep) {
        // CREATE: New step with temporary ID
        if (!planId) {
          console.error("No plan ID available for creating step");
          return;
        }

        const stepData = {
          irrigation_plan: planId,
          sector: step.sectorId,
          order: step.order,
          duration_seconds: step.durationMinutes * 60,
          fertigation_start_delay_seconds: step.fertigationStartDelayMinutes
            ? step.fertigationStartDelayMinutes * 60
            : null,
          fertigation_duration_seconds: step.fertigationDurationMinutes
            ? step.fertigationDurationMinutes * 60
            : null,
        };

        const result = await createPlanStep(stepData);
        if (result.success && result.data) {
          // Update local state with real ID
          const newStep = result.data as any;
          setPlanSteps((prev) =>
            prev.map((s) => (s.id === step.id ? { ...s, id: newStep.id } : s))
          );
        } else {
          console.error("Failed to create step:", result.error);
        }
      } else {
        // UPDATE: Existing step with real ID
        const stepData = {
          sector: step.sectorId,
          order: step.order,
          duration_seconds: step.durationMinutes * 60,
          fertigation_start_delay_seconds: step.fertigationStartDelayMinutes
            ? step.fertigationStartDelayMinutes * 60
            : null,
          fertigation_duration_seconds: step.fertigationDurationMinutes
            ? step.fertigationDurationMinutes * 60
            : null,
        };

        const result = await updatePlanStep({ id: step.id, data: stepData });
        if (!result.success) {
          console.error("Failed to update step:", result.error);
        }
      }

      // Data is already refetched by the CRUD operations
      // No need to call refetchData again
    } catch (error) {
      console.error("Error saving step:", error);
    }
  };

  const handleUpdateStep = (
    stepId: string,
    updates: Partial<IrrigationPlanStepFormData>
  ) => {
    const updatedSteps = planSteps.map((step) =>
      step.id === stepId ? { ...step, ...updates } : step
    );
    setPlanSteps(updatedSteps);
    setHasChanges(true);
  };

  const handleMoveStep = async (stepId: string, direction: "up" | "down") => {
    const currentIndex = planSteps.findIndex((step) => step.id === stepId);
    if (
      (direction === "up" && currentIndex === 0) ||
      (direction === "down" && currentIndex === planSteps.length - 1)
    ) {
      return;
    }

    const targetIndex =
      direction === "up" ? currentIndex - 1 : currentIndex + 1;

    const currentStep = planSteps[currentIndex];
    const targetStep = planSteps[targetIndex];

    // Check if both steps are existing (not new) steps
    const isCurrentStepNew = currentStep.id.startsWith("step-");
    const isTargetStepNew = targetStep.id.startsWith("step-");

    if (!isCurrentStepNew && !isTargetStepNew) {
      // Both steps are existing - use the swap operation
      try {
        const step1 = { id: currentStep.id, order: currentStep.order };
        const step2 = { id: targetStep.id, order: targetStep.order };

        await swapPlanStepsOrders(step1, step2);
        // Data is already refetched by the swap operation
        // No need to call refetchData again
      } catch (error) {
        console.error("Error swapping steps:", error);
        return;
      }
    }

    // Update local state for immediate UI feedback
    const newSteps = [...planSteps];

    // Swap steps
    [newSteps[currentIndex], newSteps[targetIndex]] = [
      newSteps[targetIndex],
      newSteps[currentIndex],
    ];

    // Update order numbers
    newSteps.forEach((step, index) => {
      step.order = index + 1;
    });

    setPlanSteps(newSteps);
    setHasChanges(true);
  };

  const handleReorderSteps = async (newOrder: IrrigationPlanStepFormData[]) => {
    // Update the order numbers to match the new sequence
    const reorderedSteps = newOrder.map((step, index) => ({
      ...step,
      order: index + 1,
    }));

    setPlanSteps(reorderedSteps);
    setHasChanges(true);
    await updatePlanStepBatch(
      reorderedSteps.map((step) => ({ id: step.id, order: step.order }))
    );
  };

  const handleSave = async () => {
    if (!projectId) return;

    try {
      // Convert form data to API model format
      const planDataToSave = {
        name: planData.name,
        description: planData.description || null,
        start_time: planData.startTime,
        days_of_week: planData.daysOfWeek,
        is_enabled: planData.isEnabled,
        fertigation_enabled: planData.fertigationEnabled,
        backwash_enabled: planData.backwashEnabled,
        start_date: planData.startDate || null,
        end_date: planData.endDate || null,
      };

      if (isCreateMode) {
        // CREATE: Create new plan with empty steps
        const result = await createPlan({
          ...planDataToSave,
          project: projectId,
          steps: [], // Create with empty steps initially
        });

        const newPlanId = result.data?.id;
        if (!newPlanId) {
          throw new Error("Failed to create irrigation plan");
        }

        // Navigate to edit mode with sectors tab visible
        setLocation(`/app/irrigation-plans/${newPlanId}?activeTab=sectors`);
      } else {
        // UPDATE: Update existing plan
        await updatePlan({ id: planId, data: planDataToSave });
        await refetchData();
        setHasChanges(false);
      }
    } catch (error) {
      console.error("Error saving irrigation plan:", error);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      setShowDiscardModal(true);
      return;
    }

    // Navigate back to appropriate page
    if (isCreateMode) {
      setLocation("/app/irrigation-plans");
    } else {
      setLocation(`/app/projects/${projectId}`);
    }
  };

  const handleConfirmDiscard = () => {
    setShowDiscardModal(false);

    // Navigate back to appropriate page
    if (isCreateMode) {
      setLocation("/app/irrigation-plans");
    } else {
      setLocation(`/app/projects/${projectId}`);
    }
  };

  const handleCancelDiscard = () => {
    setShowDiscardModal(false);
  };

  const handleManualExecution = () => {
    console.log("Starting manual execution for plan:", planId);
    // In a real app, this would trigger the irrigation plan
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    return `${minutes} min`;
  };

  const getTotalDuration = () => {
    return planSteps.reduce(
      (total, step) => total + step.durationMinutes * 60,
      0
    );
  };

  const getStatusText = (isEnabled: boolean) => {
    return isEnabled ? "Ativo" : "Inativo";
  };

  const getStatusColor = (isEnabled: boolean) => {
    return isEnabled
      ? "bg-green-100 text-green-700"
      : "bg-gray-100 text-gray-700";
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  // Only show "not found" error in edit mode
  if (!isCreateMode && !plan) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Cronograma não encontrado
          </h2>
          <p className="text-gray-600">
            O cronograma solicitado não foi encontrado ou não existe.
          </p>
        </div>
      </div>
    );
  }

  // In create mode, ensure we have a project
  if (isCreateMode && !project) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Projeto não encontrado
          </h2>
          <p className="text-gray-600">
            O projeto especificado não foi encontrado.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex  items-center justify-between">
          <div className="flex items-center gap-3">
            {/* <button
              onClick={() => setLocation(`/app/projects/${projectId}`)}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </button> */}
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                {isCreateMode ? "Novo Cronograma" : plan?.name}
              </h1>
              <div className="flex items-center gap-3 text-sm text-gray-600">
                {!isCreateMode && plan && (
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                      plan.is_enabled
                    )}`}
                  >
                    {getStatusText(plan.is_enabled)}
                  </span>
                )}
                <span>{planSteps.length} setores</span>
                <span>{formatDuration(getTotalDuration())}</span>
                {isCreateMode && project && (
                  <span className="font-medium">Projeto: {project.name}</span>
                )}
              </div>
            </div>
          </div>
          {/* <div className="flex items-center gap-2">
            <button
              onClick={handleManualExecution}
              disabled={!plan.is_enabled}
              className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
            >
              <Play className="h-4 w-4" />
              Executar
            </button>
          </div> */}
        </div>
      </div>

      {/* Tab Navigation */}
      {!isCreateMode && (
        <div className="">
          <Tabs
            tabs={IRRIGATION_PLAN_TABS}
            activeTab={activeTab as TabType}
            onTabChange={setActiveTab}
            className=""
          />
        </div>
      )}

      {/* Tab Content */}
      <div className="pb-20">
        {activeTab === "config" && (
          <>
            <IrrigationPlanConfigPanel
              data={planData}
              project={project ?? undefined}
              onChange={handlePlanDataChange}
            />
            {/* Action Buttons (Save/Cancel) */}
            <div className="flex items-center justify-end gap-3 mt-3 pt-3 px-4 border-t border-gray-200">
              <Button
                onClick={handleCancel}
                variant="secondary"
                size="md"
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancelar
              </Button>
              <Button
                onClick={handleSave}
                variant="primary"
                size="md"
                disabled={loading || !isPlanDataValid(planData)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
              >
                Salvar
              </Button>
            </div>
          </>
        )}
        {activeTab === "sectors" && (
          <IrrigationPlanStepsPanel2
            projectId={projectId!}
            planData={planData}
            steps={planSteps}
            sectors={sectors}
            hasFrequencyInverter={hasFrequencyInverter}
            onAddStep={handleAddStep}
            onRemoveStep={handleRemoveStep}
            onBulkRemoveSteps={handleBulkRemoveSteps}
            onSaveStep={handleSaveStep}
            onUpdateStep={handleUpdateStep}
            onMoveStep={handleMoveStep}
            onReorderSteps={handleReorderSteps}
            loading={loading}
            projectData={
              project
                ? {
                    pipe_wash_time_seconds: project.pipe_wash_time_seconds,
                  }
                : undefined
            }
          />
        )}
      </div>

      {/* Discard Changes Modal */}
      <ConfirmModal
        isOpen={showDiscardModal}
        onClose={handleCancelDiscard}
        onConfirm={handleConfirmDiscard}
        title="Descartar alterações"
        message="Descartar alterações não salvas?"
        confirmText="Descartar"
        cancelText="Cancelar"
        variant="warning"
      />
    </div>
  );
}

export default IrrigationPlanPage;
