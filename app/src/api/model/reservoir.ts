import type {
  DirectusRelationFieldType,
} from "@/utils/types";
import type { Point } from "geojson";
import type { Model } from "./common";
import type { Device } from "./device";
import type { Property } from "./property";
import type { WaterPump } from "./water-pump";

export type ReservoirRelationsTypes = {
  property: DirectusRelationFieldType<Property>;
  reservoir_monitor: DirectusRelationFieldType<Device>;
  water_pump: DirectusRelationFieldType<WaterPump>;
};

export type ReservoirDefaultRelationsTypes = ReservoirRelationsTypes;

export interface Reservoir<
  Types extends Partial<ReservoirRelationsTypes> = ReservoirDefaultRelationsTypes
> extends Model {
  property: Types["property"];
  name: string;
  reservoir_monitor: Types["reservoir_monitor"] | null;
  water_pump: Types["water_pump"] | null;
  description: string | null;
  capacity: number | null;
  safety_time_minutes: number | null;
  location: Point | null;
  enabled: boolean;
}
